@font-face {
    font-family: "Cabinet Grotesk Variable";
    src: url("../fonts/CabinetGrotesk_Complete/fonts/WEB/fonts/CabinetGrotesk-Variable.woff2") format("woff2"),
        url("../fonts/CabinetGrotesk_Complete/fonts/WEB/fonts/CabinetGrotesk-Variable.woff") format("woff"),
        url("../fonts/CabinetGrotesk_Complete/fonts/WEB/fonts/CabinetGrotesk-Variable.ttf") format("truetype");
    font-weight: 100 900;
    font-display: swap;
    font-style: normal;
}

:root {
    --pure-white: #ffffff;
    --dark-peach: #ed9040;
    --medium-gray: #666666;
    --steel-blue: #304f5c;
    --light-gray: #cccccc;
    --cloud-gray: #e9eaeb;
    --silver-gray: #dedede;
    --soft-light-gray: #d9d9d9;
    --emerald-green: #10b981;
    --royal-blue: #2563eb;
    --ashen-fog: #a4a4a4;
    --snow-white: #fdfdfd;
    --jet-black: #010101;
    --off-white: #f6f6f6;
    --vibrant-green: #12b76a;
    --dark-slate: #181d27;
    --gunmetal-gray: #535862;
    --soft-seafoam: #ecfdf3;
    --amber: #f79543;
    --slate-blue: #3d5a66;
    --cream-white: #fefdfc;
    --deep-ocean: #001620;
    --teal-blue: #2f505c;
    --dusty-blue: #71838d;
    --honey-gold: #f3c884;
    --golden-orange: #f5891f;
    --light-apricot: #ffdbbc;
    --dark-grayish-blue:#414651;
    --shadowed-steel:#5D5D5D;
    --grayish-green:#717680;
    --rich-black:#1B1B1B;
    --charcoal:#323232;
    --cultured:#FAFAFA;
    --copper:#EC8F40;
    --light-coral:#FB5D5D;

}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: "inter";
    background-color: var(--off-white);
}

/*Heading Font Sizes */
h1 {font-size:24px;}
h2 {font-size: 18px;}
h3 {font-size: 16px;}
h4 {font-size: 14px;}
h5 {font-size: 12px;}

h1,h2,h3,h4,h5,h6 {line-height: 1.2;word-break: break-word;margin: 0px;}
p,a,span {line-height: 1.6;word-break: break-word;text-decoration: none;}
ul,ol {margin: 0px;padding: 0px;}
li {font-size: 16px; font-family: "Inter", sans-serif;}
p {margin: 0px;}


/* custom-class */
.fs-16 {font-size: 16px;}
.fs-14 {font-size: 14px;}


.fit-content {width: fit-content;}
.border-radius-10 {border-radius: 10px;}
.border-radius-5 {border-radius: 5px;}

/* custom-colors */
.text-white {color: var(--pure-white);}
.jet-black {color: var(--jet-black);}
.text-primary {color: var(--golden-orange);}
.text-secondary {color: var(--medium-gray);}
.text-success {color: var(--emerald-green);}
.text-info {color: var(--royal-blue);}
.text-warning {color: var(--amber);}
.text-danger {color: var(--vibrant-green);}
.steel-blue{color: var(--steel-blue);}
.dark-slate{color: var(--dark-slate);}
.gunmetal-gray {color: var(--gunmetal-gray);}
.dark-grayish-blue{color: var(--dark-grayish-blue);}
.dark-peach{color: var(--dark-peach);}
.shadowed-steel{color: var(--shadowed-steel);}
.grayish-green{color: var(--grayish-green);}
.rich-black{color: var(--rich-black);}
.charcoal{color: var(--charcoal);}
.copper-text{color: var(--copper);}
.light-coral{color: var(--light-coral);}

/* bg-color */
.bg-white {background-color: var(--pure-white);}
.bg-cultured {background-color: var(--cultured);}

/* font-weight  */
.fw-300{font-weight: 300;}
.fw-400{font-weight: 400;}
.fw-500{font-weight: 500;}
.fw-600{font-weight: 600;}
.fw-700 {font-weight: 700;}

/* Cabinet Grotesk Variable Font Weight Utilities */
.font-thin { font-family: "Cabinet Grotesk Variable"; font-weight: 100; }
.font-extralight { font-family: "Cabinet Grotesk Variable"; font-weight: 200; }
.font-light { font-family: "Cabinet Grotesk Variable"; font-weight: 300; }
.font-regular { font-family: "Cabinet Grotesk Variable"; font-weight: 400; }
.font-medium { font-family: "Cabinet Grotesk Variable"; font-weight: 500; }
.font-semibold { font-family: "Cabinet Grotesk Variable"; font-weight: 600; }
.font-bold { font-family: "Cabinet Grotesk Variable"; font-weight: 700; }
.font-extrabold { font-family: "Cabinet Grotesk Variable"; font-weight: 800; }
.font-black { font-family: "Cabinet Grotesk Variable"; font-weight: 900; }

/* Typography Utilities */
.cabinet-grotesk { font-family: "Cabinet Grotesk Variable"; }
.inter { font-family: "Inter", sans-serif; }

[data-kt-app-layout=dark-sidebar] .app-sidebar .app-sidebar-logo {border-bottom: 0;}
[data-kt-app-layout=dark-sidebar] .app-sidebar {border-right: 1px solid var(--golden-orange);background: var(--deep-ocean);backdrop-filter: blur(2px);}

/* global-class */
.border-bottom{border-bottom: 1px solid #E7E7E7;}

/* Sidebar Menu Styling */
.app-sidebar .menu-link { transition: all 0.3s ease;border-radius: 8px;margin-bottom: 4px;}
.app-sidebar .menu-link:hover {transform: translateX(4px);}
.app-sidebar .menu-sub .menu-link:hover {background: rgba(113, 131, 141, 0.4) !important;transform: translateX(2px);}
.app-sidebar .menu-arrow {transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);}
.app-sidebar .menu-arrow i {transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);}
.app-sidebar .menu-sub {overflow: hidden;transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);transform-origin: top;}
.app-sidebar .menu-sub.opening {animation: slideDown 0.5s cubic-bezier(0.4, 0, 0.2, 1) forwards;}
.app-sidebar .menu-sub.closing {animation: slideUp 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;}

@keyframes slideDown {0% {opacity: 0;transform: translateY(-15px) scaleY(0.8);max-height: 0;}
    50% {opacity: 0.5;transform: translateY(-5px) scaleY(0.9);}
    100% {opacity: 1;transform: translateY(0) scaleY(1);max-height: 350px;}}
@keyframes slideUp {
    0% {opacity: 1;transform: translateY(0) scaleY(1);max-height: 350px;}
    50% {opacity: 0.5;transform: translateY(-5px) scaleY(0.9);}
    100% {opacity: 0;transform: translateY(-15px) scaleY(0.8);max-height: 0;}}

/* Additional smooth effects */
.app-sidebar .menu-sub .menu-item:nth-child(1) { animation-delay: 0.1s; }
.app-sidebar .menu-sub .menu-item:nth-child(2) { animation-delay: 0.15s; }
.app-sidebar .menu-sub .menu-item:nth-child(3) { animation-delay: 0.2s; }
.app-sidebar .menu-sub .menu-item:nth-child(4) { animation-delay: 0.25s; }
.app-sidebar .menu-sub .menu-item:nth-child(5) { animation-delay: 0.3s; }
.app-sidebar .menu-sub .menu-item {transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);transform: translateX(0);}

/* Scrollbar Styling */
.app-sidebar-menu .scroll-y::-webkit-scrollbar {width: 4px;}

.app-sidebar-menu .scroll-y::-webkit-scrollbar-thumb:hover {background: var(--amber);}
.app-sidebar-menu .scroll-y::-webkit-scrollbar-thumb {background: var(--golden-orange);border-radius: 2px;}
.app-sidebar-menu .scroll-y::-webkit-scrollbar-track {background: rgba(255, 255, 255, 0.1);border-radius: 2px;}

[data-kt-app-layout=dark-sidebar] .app-sidebar .menu>.menu-item .menu-link {color: #dbdfe9;border-radius: 10px;background: rgba(255, 255, 255, 0.10);}
[data-kt-app-layout=dark-sidebar] .app-sidebar .menu>.menu-item .menu-link.active {border: 1px solid var(--silver-gray);background: rgba(255, 255, 255, 0.30);backdrop-filter: blur(12px);}


#kt_app_page {background-color:#ffffff ;}
/* background: url(../media/images/bg-texture.png) lightgray 0% 0% / 105.46875px 105.46875px repeat; */

input:focus-visible {outline: 0;}
.card-inner-main .card-inner {width: 390px;} 
.stat-card {border-radius: 5px;border: 1px solid var(--silver-gray);background: var(--off-white);padding: 10px;}

/* Filter-btn  */
.btn-filters:focus {border: 0;}
.btn-filters:focus-visible {outline: 0;border: 0;}
.btn-filters { border-radius: 8px; background: var(--steel-blue); box-shadow: 0px 1px 2px 0px rgba(10, 13, 18, 0.05); display: flex; height: 45px; padding: 10px 16px; justify-content: center; align-items: center; gap: 8px; border: 0; color: #fff; }


/* create-btn */
.btn-create:hover, .btn-save:hover {background: linear-gradient(90deg, #915827 0%, #F79643 80%); color: white;}
.btn-create, .btn-save { background: linear-gradient(90deg, #F79643 0%, #915827 100%);border: none; padding: 8px 20px; display: flex; align-items: center; gap: 8px; height: 45px; }

/* Tabs-sec */
.tabs-section .nav-tabs .nav-item {border-right: 1px solid var(--Gray-300, #D5D7DA);background: #FFF;}
.tabs-section .nav-tabs .nav-link.active {border-right: 1px solid var(--Gray-300, #D5D7DA);background: #FAFAFA;border-top: 0;border-radius: 0;}
.tabs-section .nav-tabs .nav-link:focus, .tabs-section .nav-tabs .nav-link:hover { margin: 0; }
.tabs-section .nav-tabs .nav-item .nav-link { color: #414651;opacity: 0.7;border-top: 0;border-radius: 0;}
.tabs-section .nav-tabs {border-radius: 8px;border: 1px solid var(--Gray-300, #D5D7DA);box-shadow: 0px 1px 2px 0px rgba(10, 13, 18, 0.05);overflow-y: hidden;}


.cursor-default{cursor: default;}

/* Table */
.user-table tr {border-top: 0;}
.user-table tr th:last-child {border-radius: 0 10px 0 0;}
.user-table tr th:first-child {border-radius: 10px 0 0 0;}
.user-info {display: flex;align-items: center;gap: 10px;}
.user-table tbody td { padding: 16px 24px; vertical-align: middle; border-bottom: 1px solid #E9EAEB; border: 0; color: var(--gunmetal-gray); }
.user-avatar { width: 40px; height: 40px; object-fit: cover; border-radius: 50px; box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.10); }
.user-table tr th { background-color: var(--steel-blue); border: 0; color: var(--pure-white); padding: 12px 24px; font-size: 12px; font-weight: 500; font-family: 'Inter'; }
.table-container { border-radius: 12px; border: 1px solid var(--Gray-200, #E9EAEB); background: var(--pure-white); box-shadow: 0px 1px 3px 0px rgba(10, 13, 18, 0.10), 0px 1px 2px 0px rgba(10, 13, 18, 0.06); }
.custom-tabel-footer {padding: 0 14px; }
.dataTables_wrapper .dataTables_paginate {display: flex;justify-content: space-between;align-items: center;}
.custom-tabel-footer .dataTables_info {display: none !important;}
.dataTables_paginate .paginate_button { cursor: pointer; color: var(--dark-grayish-blue); border-radius: 8px; border: 1px solid var(--Gray-300, #D5D7DA); background: var(--White, #FFF); box-shadow: 0px 1px 2px 0px rgba(10, 13, 18, 0.05); padding: 8px 14px; }
.dataTables_paginate .paginate_button i {color: var(--dark-grayish-blue);}
.toggle-slider { position: absolute; cursor: pointer; top: 0; left: 0; right: 0; bottom: 0; background-color: #F5F5F5; transition: 0.3s; border-radius: 28px; }
input:checked+.toggle-slider {background-color: var(--steel-blue);}
.toggle-slider:before { position: absolute; content: ""; height: 22px; width: 22px; left: 3px; bottom: 3px; background-color: white; transition: 0.3s; border-radius: 50%; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2); }
input:checked+.toggle-slider:before {transform: translateX(24px);}
.status-toggle input {opacity: 0;width: 0;height: 0;}
.status-text {font-size: 14px;font-weight: 500;font-family: 'Inter';}
.status-active {color: #414651;}
.status-inactive {color: #dc3545;}
.action-buttons a { border-radius: 5px; border: 1px solid #D9D9D9; display: flex; width: 30px; height: 30px; padding: 5px; justify-content: center; align-items: center; gap: 10px; }
.action-buttons i {color: var(--dark-peach);}
.user-table tbody tr:hover {background-color: var(--pure-white);}
.dataTables_paginate span {display: flex;gap: 10px;}
.dataTables_wrapper .dataTables_paginate .paginate_button.current { border-radius: 8px; background: #71838D; display: flex; width: 40px; height: 40px; justify-content: center; align-items: center; color: var(--pure-white); }
.custom-search input { border: 1px solid var(--Gray-300, #D5D7DA); background: var(--White, #FFF); box-shadow: 0px 1px 2px 0px rgba(10, 13, 18, 0.05); border-radius: 8px; width: 100%; height: 45px; padding: 8px 31px; }
.custom-search .fa-search { position: absolute; left: 12px; top: 50%; transform: translateY(-50%); color: #6c757d; }
.dataTables_wrapper .dataTables_length, .dataTables_wrapper .dataTables_filter {display: none;}
.dataTables_wrapper .dataTables_info {color: #6c757d;font-size: 14px;}
.user-table thead th.sorting, .user-table thead th.sorting_asc, .user-table thead th.sorting_desc {cursor: default !important;}
.user-table thead th.sorting:after, .user-table thead th.sorting_asc:after, .user-table thead th.sorting_desc:after {display: none !important;}
.role-badge {padding: 4px 12px;border-radius: 20px;font-size: 12px;font-weight: 500; }
.role-admin { background: #e3f2fd;color: #1976d2;}
.role-worker { background: #fff3e0; color: #f57c00; }
.role-commercial {background: #f3e5f5;color: #7b1fa2;}
.status-toggle { position: relative; display: inline-block; width: 52px; height: 28px; margin-right: 12px; }
.action-buttons {display: flex;gap: 8px; }
.btn-action { width: 36px; height: 36px; border-radius: 8px; border: none; display: flex; align-items: center; justify-content: center; font-size: 14px; cursor: pointer; transition: all 0.2s ease; }
.custom-search {position: relative;width: 250px;}
.filter-dropdown-menu { padding: 15px; min-width: 220px; border: 0; box-shadow: 0px 0px 50px 0px rgba(82, 63, 105, 0.15); border-radius: 8px; }
.filter-dropdown-menu .dropdown-header { color: #5a6c7d; font-weight: 600; padding: 8px 0; font-size: 13px; }
.filter-dropdown-menu .dropdown-item {padding: 8px 0; }
.filter-dropdown-menu .dropdown-item:hover {background-color: transparent;}
.filter-dropdown-menu .form-check-input {cursor: pointer;}
.filter-dropdown-menu .form-check-label {cursor: pointer;font-size: 14px;color: #6c757d;}
.filter-dropdown-menu .dropdown-divider {margin: 10px 0;border-top: 1px solid #f1f3f4;}
.filter-dropdown-menu .btn-primary {background-color: #5a6c7d;border-color: #5a6c7d;font-size: 14px;padding: 8px 15px;}
.filter-dropdown-menu .btn-primary:hover {background-color: #4a5b6d;border-color: #4a5b6d;}
.fit-content {border-radius: 8px;border: 1px solid  #D5D7DA;box-shadow: 0px 1px 2px 0px rgba(10, 13, 18, 0.05);overflow: hidden;}
.tabs-section .nav-link {border-right: 1px solid var(--Gray-300, #D5D7DA);background: #FFF;padding: 10px 16px;border-radius: 0;color: #414651;opacity: 0.7;}
.tabs-section .nav-item {margin: 0;}
.tabs-section .nav-link.active { border-right: 1px solid var(--Gray-300, #D5D7DA); background: #FAFAFA; color: #304F5C; opacity: 1;}
/* .user-table tbody tr {white-space: nowrap;overflow: hidden;} */
.user-table td, .user-table th {white-space: nowrap;width: fit-content;}
.table-container { overflow-x: auto;}
.table-container::-webkit-scrollbar {height: 5px;}
.table-container::-webkit-scrollbar-thumb { background-color: var(--steel-blue);border-radius: 4px;}

/* Create-New-User */
.btn-cancel { border-radius: 10px; border: 1px solid var(--steel-blue); background: #FFF; padding: 10px 15px; color:#1B1B1B;}
.create-user-form-inner { border-radius: 12px; border: 1px solid #E7E7E7; background: rgba(255, 255, 255, 0.33); backdrop-filter: blur(22.5px); padding: 20px; }
.password-toggle {position: absolute;top: 11px;right: 8px;cursor: pointer;}
.password-toggle i {color: #9CA3AF;}

.sidebar-botton-sec { position: absolute; bottom: 0; width: 90%; }
.footer-btn i {color: var(--pure-white);}
.menu-sub-accordion .active-submenu {background: rgba(113, 131, 141, 0.548) !important;}


/* previous-wearehouse-suppliers */
.shuffle-content .nav-item .nav-link.active i {color: var(--pure-white);}
.shuffle-content .nav-item .nav-link.active { border-radius: 9px;background: var(--steel-blue);color: var(--pure-white);}
.shuffle-content {border-radius: 9px;border: 1.125px solid  #D5D7DA;background: var(--pure-white);box-shadow: 0px 1.125px 2.25px 0px rgba(10, 13, 18, 0.05);}
.user-info-dropdown .dropdown-icon {cursor: pointer;}


.price-good {background-color: #ECFDF3 !important;color: #027A48 !important;}
.price-high {background-color: #FEF3F2 !important;color:#B42318 !important;}

/* btn-edit-delete */
.btn-delete { border-radius: 10px; background: var(--steel-blue); display: flex; width: 90px; height: 41px; justify-content: center; align-items: center; gap: 8px; }
.btn-delete:hover { background-color: transparent; border: 1px solid var(--steel-blue); color: var(--steel-blue) !important; }
.btn-delete:hover i {color: var(--steel-blue) !important;}

/* materials-show */
.create-material-details {border-radius: 12px;border: 1px solid #E7E7E7;background: rgba(255, 255, 255, 0.33);backdrop-filter: blur(22.5px);padding: 24px;}
.create-material-details-inner { border-radius: 12px; border: 1px solid #EEE; background: #FAFAFA; padding: 20px; }