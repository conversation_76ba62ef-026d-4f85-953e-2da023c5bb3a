<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\ThemeController;


use App\Http\Controllers\RoleController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\CrudGeneratorController;
use App\Http\Controllers\DashboardController;
use Spatie\Permission\Models\Permission;
use Illuminate\Support\Facades\Auth;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

Route::get('/test', function () {
    $permissions = Permission::pluck('id', 'id')->all();
    return $permissions; // Ensure you are returning the variable here
});
// Route::get('/', function () {
//     return view('website.index');
// });
Route::get('/logout', function () {
    Auth::logout();
    return redirect('/'); 
});
Route::get('/', function () {
    if (auth()->user()) {
        return redirect('home');
    } else {
        return view('auth.login');
    }
})->middleware('auth');
Route::get('crud_generator', [CrudGeneratorController::class, 'crudGenerator'])->name('crud_generator');
Route::post('crud_generator_process', [CrudGeneratorController::class, 'crudGeneratorProcess'])->name('crud_generator_process');

Route::get('permissions', action: [ThemeController::class, 'permissions'])->name('permissions')->middleware('auth');
Auth::routes();
Route::resource("settings", "\App\Http\Controllers\SettingsController")->middleware("auth");




Route::group(['middleware' => ['auth']], function () {
    Route::resource('roles', RoleController::class);
    Route::resource('users', UserController::class);
});

// Admin-Dashboard
Route::middleware(['auth'])->group(function () {
    Route::get('/home', [DashboardController::class, 'dashboard'])->name('home');
    Route::get('/user-management', [DashboardController::class, 'userManagement'])->name('user-management');
    Route::get('/create-new-user', [DashboardController::class, 'createNewUser'])->name('create-new-user');
    Route::get('/suppliers', [DashboardController::class, 'suppliers'])->name('suppliers');
    Route::get('/materials', [DashboardController::class, 'materials'])->name('materials');
    Route::get('/create-material', [DashboardController::class, 'createMaterial'])->name('create-material');
    Route::get('/show-material', [DashboardController::class, 'showMaterial'])->name('show-material');
    // abhi-zorrat nh hai
    Route::get('/purchase-orders', [DashboardController::class, 'purchaseOrders'])->name('purchase-orders');
    Route::get('/create-order', [DashboardController::class, 'createOrder'])->name('create-order');
    Route::get('/draft-order', [DashboardController::class, 'draftOrder'])->name('draft-order');
    Route::get('/warehouse', [DashboardController::class, 'warehouse'])->name('warehouse');
    Route::get('/material-calendar', [DashboardController::class, 'materialCalendar'])->name('material-calendar');
    Route::get('/create-supplier', [DashboardController::class, 'createSupplier'])->name('create-supplier');
    Route::get('/jumbo-stocks', [DashboardController::class, 'jumboStocks'])->name('jumbo-stocks');
    
});
