<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Spatie\Permission\Models\Role;

class Profile extends Model
{
    protected $guarded= [];

    protected $fillable = [
        'user_id', 'bio', 'gender','dob','age','pic','country','state','city','address','postal'];

    public function user(){
        return $this->belongsTo(User::class);
    }

    public function user_roles(){
        return $this->belongsTo(Role::class);
    }
}