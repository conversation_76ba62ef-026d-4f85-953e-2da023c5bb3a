<?php $__env->startSection('title','Materials'); ?>

<?php $__env->startPush('css'); ?>
<style>

</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<section class="tab-table-sec">
    <div class="container">
        <div class="row">
            <div class="col-lg-12 p-0 my-4">
                <!-- Header Section -->
                <div class="header-section">
                    <div class="d-flex justify-content-between align-items-center">
                        <h1 class="font-bold steel-blue">MATERIALS</h1>
                        <div class="header-controls d-flex align-items-center gap-4">
                            <div class="custom-search">
                                <i class="fas fa-search"></i>
                                <input type="text" id="customSearch" placeholder="Search">
                            </div>
                            <div class="dropdown filter-dropdown">
                                <button class="btn-filters fs-14" data-bs-toggle="dropdown" aria-expanded="false">
                                    <img src="<?php echo e(asset('website/assets/media/images/filters-lines-icon.svg')); ?>" alt="Filter" class="img-fluid" width="20" height="20">
                                    Filters
                                </button>
                                <div class="dropdown-menu filter-dropdown-menu">
                                    <h6 class="dropdown-header">Filter by Status</h6>
                                    <div class="dropdown-item">
                                        <div class="form-check">
                                            <input class="form-check-input filter-check" type="checkbox" value="active" id="filterActive">
                                            <label class="form-check-label" for="filterActive">
                                                Active
                                            </label>
                                        </div>
                                    </div>
                                    <div class="dropdown-item">
                                        <div class="form-check">
                                            <input class="form-check-input filter-check" type="checkbox" value="inactive" id="filterInactive">
                                            <label class="form-check-label" for="filterInactive">
                                                Inactive
                                            </label>
                                        </div>
                                    </div>

                                    <div class="dropdown-divider"></div>

                                    <h6 class="dropdown-header">Filter by Role</h6>
                                    <div class="dropdown-item">
                                        <div class="form-check">
                                            <input class="form-check-input filter-check" type="checkbox" value="Admin Employees" id="filterAdmin">
                                            <label class="form-check-label" for="filterAdmin">
                                                Admin Employees
                                            </label>
                                        </div>
                                    </div>
                                    <div class="dropdown-item">
                                        <div class="form-check">
                                            <input class="form-check-input filter-check" type="checkbox" value="Workers" id="filterWorkers">
                                            <label class="form-check-label" for="filterWorkers">
                                                Workers
                                            </label>
                                        </div>
                                    </div>
                                    <div class="dropdown-item">
                                        <div class="form-check">
                                            <input class="form-check-input filter-check" type="checkbox" value="Commercial Employee" id="filterCommercial">
                                            <label class="form-check-label" for="filterCommercial">
                                                Commercial Employees
                                            </label>
                                        </div>
                                    </div>

                                    <div class="dropdown-divider"></div>

                                    <div class="dropdown-item">
                                        <button class="btn btn-sm btn-primary w-100" id="applyFilters">Apply Filters</button>
                                    </div>
                                </div>
                            </div>
                            <a href="<?php echo e(route('create-material')); ?>" class="btn-create fs-14 text-white border-radius-5">
                                <i class="fas fa-plus text-white"></i>
                                Create New Material
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-5">
                <!-- Tabs Section -->
            </div>
            <div class="col-lg-12">
                <div class="main-container mt-10">
                    <!-- Table Section -->
                    <div class="table-container">
                        <table id="userTable" class="table table-striped table-bordered user-table" style="width:100%">
                            <thead>
                                <tr>
                                    <th>Code</th>
                                    <th>Material Name </th>
                                    <th>Reference</th>
                                    <th>Adhesive</th>
                                    <th>Grammage (g/m²)</th>
                                    <th>Microns (µm)</th>
                                    <th>Liner</th>
                                    <th>HS Code</th>
                                    <th>Tariff (%)</th>
                                    <th>Actions </th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php for($i=0; $i<=10; $i++): ?>
                                    <tr data-role="Admin Employees">
                                    <td class="fs-14 fw-400 gunmetal-gray">001</td>
                                    <td class="fs-14 fw-400 gunmetal-gray">Paper Label Matte</td>
                                    <td class="fs-14 fw-400 gunmetal-gray">REF-MAT-001</td>
                                    <td class="fs-14 fw-400 gunmetal-gray">Hot Melt</td>
                                    <td class="fs-14 fw-400 gunmetal-gray">80</td>
                                    <td class="fs-14 fw-400 gunmetal-gray">60</td>
                                    <td class="fs-14 fw-400 gunmetal-gray">Glassine</td>
                                    <td class="fs-14 fw-400 gunmetal-gray">48211010</td>
                                    <td class="fs-14 fw-400 gunmetal-gray">5%</td>
                                    <td>
                                        <div class="action-buttons">
                                            <a href="<?php echo e(route('show-material')); ?>">
                                                <i class="fa-regular fa-eye"></i>
                                            </a>
                                            <a href="#" class="btn-edit"">
                                                        <i class=" fas fa-edit"></i>
                                            </a>
                                            <a href="#" class="btn-edit"">
                                                        <i class=" fa-solid fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                    </tr>
                                    <?php endfor; ?>
                            </tbody>
                        </table>
                    </div>

                </div>
            </div>
        </div>
</section>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('js'); ?>



<?php $__env->stopPush(); ?>
<?php echo $__env->make('theme.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\xampp\htdocs\git\oka_paper\resources\views/dashboard/admin/previous-warehouse/materials/index.blade.php ENDPATH**/ ?>