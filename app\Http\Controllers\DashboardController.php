<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class DashboardController extends Controller
{
    public function dashboard()
    {
        return view('dashboard.templates.admin-index');
    }
    public function permissions()
    {
        return view('theme.user-management.permissions');
    }

    public function userManagement()
    {
        return view('dashboard.admin.user-management.index');
    }
    public function createNewUser()
    {
        return view('dashboard.admin.user-management.create');
    }
    public function suppliers()
    {
        return view('dashboard.admin.previous-warehouse.suppliers.index');
    }
    public function materials()
    {
        return view('dashboard.admin.previous-warehouse.materials.index');
    } 
    public function createSupplier()
    {
        return view('dashboard.admin.previous-warehouse.suppliers.create');
    }
}
