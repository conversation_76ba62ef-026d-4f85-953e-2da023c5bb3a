<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class DashboardController extends Controller
{
    public function dashboard()
    {
        return view('dashboard.templates.admin-index');
    }
    public function permissions()
    {
        return view('theme.user-management.permissions');
    }

    public function userManagement()
    {
        return view('dashboard.admin.user-management.index');
    }
    public function createNewUser()
    {
        return view('dashboard.admin.user-management.create');
    }
    public function suppliers()
    {
        return view('dashboard.admin.previous-warehouse.suppliers.index');
    }
    public function materials()
    {
        return view('dashboard.admin.previous-warehouse.materials.index');
    } 
    public function createSupplier()
    {
        return view('dashboard.admin.previous-warehouse.suppliers.create');
    }
    public function createMaterial()
    {
        return view('dashboard.admin.previous-warehouse.materials.create');
    }
    public function showMaterial()
    {
        return view('dashboard.admin.previous-warehouse.materials.show');
    }
    public function purchaseOrders()
    {
        return view('dashboard.admin.previous-warehouse.purchase-order.index');
    }
    public function createOrder()
    {
        return view('dashboard.admin.previous-warehouse.purchase-order.create');
    }
    public function draftOrder()
    {
        return view('dashboard.admin.previous-warehouse.purchase-order.draft');
    }
    public function warehouse()
    {
        return view('dashboard.admin.previous-warehouse.warehouse.index');
    }
    public function materialCalendar()
    {
        return view('dashboard.admin.previous-warehouse.material-calendar.index');
    }
    public function jumboStocks()
    {
        return view ('dashboard.admin.jumbo-stocks.index');
    }


}
