@extends('theme.layout.master')
@section('title','Warehouse')
@push('css')
<style>
</style>
@endpush
@section('content')
<section class="tab-table-sec warehouse-table">
    <div class="container-fluid">
        <div class="row">
            <div class="col-lg-12 p-0 my-4">
                <div class="header-section">
                    <div class="d-flex justify-content-between align-items-center">
                        <h1 class="font-bold steel-blue">WAREHOUSE</h1>
                        <div class="header-controls d-flex align-items-center gap-4">
                            <div class="custom-search">
                                <i class="fas fa-search"></i>
                                <input type="text" id="customSearch" placeholder="Search">
                            </div>
                            <div class="dropdown filter-dropdown">
                                <button class="btn-filters fs-14" data-bs-toggle="dropdown" aria-expanded="false">
                                    <img src="{{ asset('website/assets/media/images/filters-lines-icon.svg') }}" alt="Filter" class="img-fluid" width="20" height="20">
                                    Filters
                                </button>
                                <div class="dropdown-menu filter-dropdown-menu">
                                    <h6 class="dropdown-header">Filter by Status</h6>
                                    <div class="dropdown-item">
                                        <div class="form-check">
                                            <input class="form-check-input filter-check" type="checkbox" value="active" id="filterActive">
                                            <label class="form-check-label" for="filterActive">
                                                Active
                                            </label>
                                        </div>
                                    </div>
                                    <div class="dropdown-item">
                                        <div class="form-check">
                                            <input class="form-check-input filter-check" type="checkbox" value="inactive" id="filterInactive">
                                            <label class="form-check-label" for="filterInactive">
                                                Inactive
                                            </label>
                                        </div>
                                    </div>

                                    <div class="dropdown-divider"></div>

                                    <h6 class="dropdown-header">Filter by Role</h6>
                                    <div class="dropdown-item">
                                        <div class="form-check">
                                            <input class="form-check-input filter-check" type="checkbox" value="Admin Employees" id="filterAdmin">
                                            <label class="form-check-label" for="filterAdmin">
                                                Admin Employees
                                            </label>
                                        </div>
                                    </div>
                                    <div class="dropdown-item">
                                        <div class="form-check">
                                            <input class="form-check-input filter-check" type="checkbox" value="Workers" id="filterWorkers">
                                            <label class="form-check-label" for="filterWorkers">
                                                Workers
                                            </label>
                                        </div>
                                    </div>
                                    <div class="dropdown-item">
                                        <div class="form-check">
                                            <input class="form-check-input filter-check" type="checkbox" value="Commercial Employee" id="filterCommercial">
                                            <label class="form-check-label" for="filterCommercial">
                                                Commercial Employees
                                            </label>
                                        </div>
                                    </div>

                                    <div class="dropdown-divider"></div>

                                    <div class="dropdown-item">
                                        <button class="btn btn-sm btn-primary w-100" id="applyFilters">Apply Filters</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-5">

                <div class="tabs-section fit-content">
                    <ul class="nav nav-pills" id="pills-tab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="pills-home-tab" data-bs-toggle="pill" data-bs-target="#pills-home" type="button" role="tab" aria-controls="pills-home" aria-selected="true">Warehouse</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="pills-profile-tab" data-bs-toggle="pill" data-bs-target="#pills-profile" type="button" role="tab" aria-controls="pills-profile" aria-selected="false">Past Warehouse</button>
                        </li>
                    </ul>

                </div>
            </div>
            <div class="col-lg-12">
                <div class="main-container mt-10">
                    <div class="tab-content" id="pills-tabContent">
                        <div class="tab-pane fade show active" id="pills-home" role="tabpanel" aria-labelledby="pills-home-tab">
                            <div class="table-container">
                                <table id="userTable" class="table table-striped table-bordered user-table" style="width:100%">
                                    <thead>
                                        <tr>
                                            <th>Purchase Order</th>
                                            <th>Material</th>
                                            <th>Supplier</th>
                                            <th>m²</th>
                                            <th>Purchase Price</th>
                                            <th>Number of Jumbos</th>
                                            <th>Length</th>
                                            <th>Width</th>
                                            <th>Purchase Date</th>
                                            <th>Port of Departure</th>
                                            <th>Departure Date</th>
                                            <th>Arrival Date</th>
                                            <th>Forwarder</th>
                                            <th>Freight</th>
                                            <th>Shipping Company</th>
                                            <th>Vessel</th>
                                            <th>Proforma</th>
                                            <th>Transfer</th>
                                            <th></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @for ($i=0; $i<=10; $i++)
                                            <tr data-role="Admin Employees">
                                            <td>
                                                PO-001
                                            </td>
                                            <td class="fs-14 fw-400 gunmetal-gray">Kraft Paper 80gsm</td>
                                            <td class="fs-14 fw-400 gunmetal-gray">AsiaPack Ltd.</span></td>
                                            <td>
                                                2200 m²
                                            </td>
                                            <td>
                                                €4,000/m²
                                            </td>
                                            <td>
                                                10
                                            </td>
                                            <td>
                                                1600mm
                                            </td>
                                            <td>
                                                1300mm
                                            </td>
                                            <td>
                                                2025-04-10
                                            </td>
                                            <td>
                                                Karachi, Port Qasim
                                            </td>
                                            <td>
                                                2025-04-12
                                            </td>
                                            <td>
                                                2025-04-22
                                            </td>
                                            <td>
                                                JetFreight Co.
                                            </td>
                                            <td>
                                                $800
                                            </td>
                                            <td>
                                                Maersk
                                            </td>
                                            <td>
                                                Evergreen
                                            </td>
                                            <td>
                                                <button class="btn btn-outline-secondary btn-sm add-document">
                                                    <i class="fas fa-plus"></i> Add Document
                                                </button>
                                            </td>
                                            <td>
                                                <button class="btn btn-outline-secondary btn-sm add-document">
                                                    <i class="fas fa-plus"></i> Add Document
                                                </button>
                                            </td>
                                            <td>
                                                <div class="action-buttons">
                                                    <a href="#">
                                                        <i class="fa-regular fa-eye"></i>
                                                    </a>
                                                    <a href="#" class="move-warehouse">
                                                        <i class="fa-solid fa-download"></i>
                                                    </a>
                                                    <a href="#" class="btn-edit">
                                                        <i class=" fas fa-edit"></i>
                                                    </a>
                                                    <a href="#" class="">
                                                        <i class=" fas fa-solid fa-trash-can"></i>
                                                    </a>
                                                    
                                                </div>
                                            </td>
                                            </tr>
                                            @endfor
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="pills-profile" role="tabpanel" aria-labelledby="pills-profile-tab">
                            <div class="table-container">
                                <table id="userTable" class="table table-striped table-bordered user-table" style="width:100%">
                                    <thead>
                                        <tr>
                                            <th>Purchase Order</th>
                                            <th>Material</th>
                                            <th>Supplier</th>
                                            <th>m²</th>
                                            <th>Purchase Price</th>
                                            <th>Number of Jumbos</th>
                                            <th>Length</th>
                                            <th>Width</th>
                                            <th>Purchase Date</th>
                                            <th>Port of Departure</th>
                                            <th>Departure Date</th>
                                            <th>Arrival Date</th>
                                            <th>Forwarder</th>
                                            <th>Freight</th>
                                            <th>Shipping Company</th>
                                            <th>Vessel</th>
                                            <th>Invoice #</th>
                                            <th>Proforma</th>
                                            <th>Transfer</th>
                                            <th>Commercial Invoice</th>
                                            <th>Transfer Receipt</th>
                                            <th>Packing List</th>
                                            <th>Freight Cost</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @for ($i=0; $i<=10; $i++)
                                            <tr data-role="Workers">
                                            <td>
                                                PO-001
                                            </td>
                                            <td class="fs-14 fw-400 gunmetal-gray">Kraft Paper 80gsm</td>
                                            <td class="fs-14 fw-400 gunmetal-gray">AsiaPack Ltd.</span></td>
                                            <td>
                                                2200 m²
                                            </td>
                                            <td>
                                                €4,000/m²
                                            </td>
                                            <td>
                                                10
                                            </td>
                                            <td>
                                                1600mm
                                            </td>
                                            <td>
                                                1300mm
                                            </td>
                                            <td>
                                                2025-04-10
                                            </td>
                                            <td>
                                                Karachi, Port Qasim
                                            </td>
                                            <td>
                                                2025-04-12
                                            </td>
                                            <td>
                                                2025-04-22
                                            </td>
                                            <td>
                                                JetFreight Co.
                                            </td>
                                            <td>
                                                $800
                                            </td>
                                            <td>
                                                Maersk
                                            </td>
                                            <td>
                                                Evergreen
                                            </td>
                                            <td>
                                                INV-0012
                                            </td>
                                            <td>
                                                <button class="btn btn-outline-secondary btn-sm download-pdf">
                                                    <i class="fa-solid fa-paperclip"></i> Proforma.pdf
                                                </button>
                                            </td>
                                             <td>
                                                <button class="btn btn-outline-secondary btn-sm download-pdf">
                                                    <i class="fa-solid fa-paperclip"></i> Transfer.pdf
                                                </button>
                                            </td>
                                             <td>
                                                <button class="btn btn-outline-secondary btn-sm download-pdf">
                                                    <i class="fa-solid fa-paperclip"></i> Invoice.pdf
                                                </button>
                                            </td>
                                             <td>
                                                <button class="btn btn-outline-secondary btn-sm download-pdf">
                                                    <i class="fa-solid fa-paperclip"></i> Receipt.pdf
                                                </button>
                                            </td>
                                             <td>
                                                <button class="btn btn-outline-secondary btn-sm download-pdf">
                                                    <i class="fa-solid fa-paperclip"></i> Packing List.pdf
                                                </button>
                                            </td>
                                             <td>
                                                <button class="btn btn-outline-secondary btn-sm download-pdf">
                                                    <i class="fa-solid fa-paperclip"></i> Freight Cost.pdf
                                                </button>
                                            </td>
                                            </tr>
                                            @endfor
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="pills-contact" role="tabpanel" aria-labelledby="pills-contact-tab">
                            <div class="table-container">
                                <table id="userTable" class="table table-striped table-bordered user-table" style="width:100%">
                                    <thead>
                                        <tr>
                                            <th>NAME</th>
                                            <th>EMAIL</th>
                                            <th>ROLE</th>
                                            <th>STATUS</th>
                                            <th>ACTIONS</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @for ($i=0; $i<=10; $i++)
                                            <tr data-role="Commercial Employee">
                                            <td>
                                                <div class="user-info">
                                                    <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face" alt="Claire Hauck-Bashirian" class="user-avatar">
                                                    <div>
                                                        <div class="dark-slate fs-14 fw-500">Claire Hauck-Bashirian</div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="fs-14 fw-400 gunmetal-gray"><EMAIL></td>
                                            <td><span class="role-badge role-commercial">Commercial Employee</span></td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <label class="status-toggle">
                                                        <input type="checkbox" onchange="toggleStatusStatic(this)">
                                                        <span class="toggle-slider"></span>
                                                    </label>
                                                    <span class="status-text status-inactive">Deactive</span>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="action-buttons">
                                                    <button class="btn-action btn-edit">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button class="btn-action btn-delete">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                            </tr>
                                            @endfor
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
</section>


<!-- Include Move Warehouse Modal -->
@include('dashboard.templates.modal.move-warehouse')

@endsection

@push('js')
<script>
    $(document).ready(function() {
        // Handle move warehouse click
        $('.move-warehouse').on('click', function(e) {
            e.preventDefault();
            $('#confirmationModal').modal('show');
        });
    });
</script>
@endpush