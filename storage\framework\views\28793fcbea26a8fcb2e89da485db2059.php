<?php $__env->startSection('title','Create - Rafa'); ?>
<?php $__env->startPush('css'); ?>


<?php $__env->stopPush(); ?>


<?php $__env->startSection('content'); ?>
<section class="crete-user-sec">
    <div class="container">
        <div class="row">
            <div class="col-lg-12 p-0">
                <div class="create-user-form">
                    <!-- Header -->
                    <div class="d-flex justify-content-between align-items-center mb-10 mt-5">
                        <h1 class="font-bold steel-blue">REGISTER NEW MATERIAL</h1>
                        <div class="d-flex gap-4">
                            <a href="<?php echo e(route('materials')); ?>" type="button" class="btn-cancel">Cancel</a>
                            <button type="button" class="btn-create fs-14 text-white border-radius-10">Save</button>
                        </div>
                    </div>

                    <!-- Form -->
                    <div class="create-user-form-inner">
                        <h2 class="font-bold steel-blue mb-6">GENRAL DATA</h2>
                        <!-- <h2 class="section-title">ENTER USER DETAILS</h2> -->
                        <form>
                            <div class="row mb-5">
                                <div class="col-md-6">
                                    <label for="userName" class="shadowed-steel fs-16 fw-500 mb-2">Material</label>
                                    <input type="text" class="form-control" id="" placeholder="Enter material name">
                                </div>
                                <div class="col-md-6">
                                    <label for="userEmail" class="shadowed-steel fs-16 fw-500 mb-2">Reference</label>
                                    <input type="email" class="form-control" id="" placeholder="Enter reference">
                                </div>
                            </div>

                            <div class="row mb-5">
                                <div class="col-md-6">
                                    <label for="" class="shadowed-steel fs-16 fw-500 mb-2">Main Supplier</label>
                                    <select class="form-select" id="userRole">
                                        <option value="" selected>Enter main supplier</option>
                                        <option value="admin">Admin</option>
                                        <option value="user">User</option>
                                        <option value="manager">Manager</option>
                                        <option value="viewer">Viewer</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="userStatus" class="shadowed-steel fs-16 fw-500 mb-2">Price</label>
                                    <input type="number" class="form-control" id="userStatus" placeholder="Enter Price">
                                </div>
                            </div>

                            <div class="row mb-5">
                                <div class="col-md-6">
                                    <label for="userRole" class="shadowed-steel fs-16 fw-500 mb-2">Code</label>
                                    <input type="email" class="form-control" id="userEmail" placeholder="Enter code">
                                </div>
                                <div class="col-md-6">
                                    <label for="userStatus" class="shadowed-steel fs-16 fw-500 mb-2">Adhesive</label>
                                    <input type="number" class="form-control" id="userStatus" placeholder="Enter adhesive">
                                </div>
                            </div>

                            <div class="row mb-5">
                                <div class="col-md-6">
                                    <label for="" class="shadowed-steel fs-16 fw-500 mb-2">Grammage</label>
                                    <input type="text" class="form-control" id="" placeholder="Enter grammage">
                                </div>
                                <div class="col-md-6">
                                    <label for="" class="shadowed-steel fs-16 fw-500 mb-2">Microns</label>
                                    <input type="text" class="form-control" id="" placeholder="Enter microns">
                                </div>
                            </div>

                            <div class="row mb-5">
                                <div class="col-md-6">
                                    <label for="" class="shadowed-steel fs-16 fw-500 mb-2">Liner</label>
                                    <input type="text" class="form-control" id="" placeholder="Enter Liner">
                                </div>
                                <div class="col-md-6">
                                    <label for="" class="shadowed-steel fs-16 fw-500 mb-2">HS Code</label>
                                    <input type="text" class="form-control" id="" placeholder="Enter HS Code">
                                </div>
                            </div>

                            <div class="row mb-5">
                                <div class="col-md-6">
                                    <label for="" class="shadowed-steel fs-16 fw-500 mb-2">Tariff</label>
                                    <input type="text" class="form-control" id="userStatus" placeholder="Enter tariff">
                                </div>
                                <div class="col-md-6">
                                    <label for="" class="shadowed-steel fs-16 fw-500 mb-2">Material Color</label>
                                    <select class="form-select" id="">
                                        <option value="" selected>Select material color</option>
                                        <option value="admin">Red</option>
                                        <option value="user">Green</option>
                                        <option value="manager">Blue</option>
                                        <option value="viewer">Yellow</option>
                                    </select>
                                </div>

                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
</section>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('js'); ?>
<!-- <script>
    function togglePassword() {
        var passwordInput = document.getElementById("userPassword");
        var passwordIcon = document.getElementById("passwordIcon");

        if (passwordInput.type === "password") {
            passwordInput.type = "text";
            passwordIcon.classList.remove("fa-eye");
            passwordIcon.classList.add("fa-eye-slash");
        } else {
            passwordInput.type = "password";
            passwordIcon.classList.remove("fa-eye-slash");
            passwordIcon.classList.add("fa-eye");
        }
    }
</script> -->
<?php $__env->stopPush(); ?>
<?php echo $__env->make('theme.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\xampp\htdocs\git\oka_paper\resources\views/dashboard/admin/previous-warehouse/materials/create.blade.php ENDPATH**/ ?>