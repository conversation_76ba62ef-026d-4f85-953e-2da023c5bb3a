<?php $__env->startSection('title','Create - Rafa'); ?>
<?php $__env->startPush('css'); ?>


<?php $__env->stopPush(); ?>


<?php $__env->startSection('content'); ?>
<section class="crete-user-sec">
    <div class="container">
        <div class="row">
            <div class="col-lg-12 p-0">
                <div class="create-user-form">
                    <!-- Header -->
                    <div class="d-flex justify-content-between align-items-center mb-10 mt-5">
                        <h1 class="font-bold steel-blue">CREATE NEW USER</h1>
                        <div class="d-flex gap-4">
                            <button type="button" class="btn-cancel">Cancel</button>
                            <button type="button" class="btn-create fs-14 text-white border-radius-10">Save</button>
                        </div>
                    </div>

                    <!-- Form -->
                    <div class="create-user-form-inner">
                        <h2 class="font-bold steel-blue mb-6">ENTER USER DETAILS</h2>
                        <!-- <h2 class="section-title">ENTER USER DETAILS</h2> -->
                        <form>
                            <div class="row mb-5">
                                <div class="col-md-6">
                                    <label for="userName" class="shadowed-steel fs-16 fw-500 mb-2">Name</label>
                                    <input type="text" class="form-control" id="userName" placeholder="Enter user name">
                                </div>
                                <div class="col-md-6">
                                    <label for="userEmail" class="shadowed-steel fs-16 fw-500 mb-2">Email</label>
                                    <input type="email" class="form-control" id="userEmail" placeholder="Enter email">
                                </div>
                            </div>

                            <div class="row mb-5">
                                <div class="col-md-6">
                                    <label for="userRole" class="shadowed-steel fs-16 fw-500 mb-2">User Role</label>
                                    <select class="form-select" id="userRole">
                                        <option value="" selected>Select</option>
                                        <option value="admin">Admin</option>
                                        <option value="user">User</option>
                                        <option value="manager">Manager</option>
                                        <option value="viewer">Viewer</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="userStatus" class="shadowed-steel fs-16 fw-500 mb-2">Status</label>
                                    <select class="form-select" id="userStatus">
                                        <option value="" selected>Select</option>
                                        <option value="active">Active</option>
                                        <option value="inactive">Inactive</option>
                                        <option value="pending">Pending</option>
                                        <option value="suspended">Suspended</option>
                                    </select>
                                </div>
                            </div>

                            <div class="row mb-5">
                                <div class="col-12">
                                    <label for="userPassword" class="shadowed-steel fs-16 fw-500 mb-2">Password</label>
                                    <div class="position-relative">
                                        <input type="password" class="form-control" id="userPassword" placeholder="Enter password">
                                        <a type="" class="password-toggle" onclick="togglePassword()">
                                            <i class="fa-regular fa-eye" id="passwordIcon"></i>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
</section>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('js'); ?>
<!-- <script>
    function togglePassword() {
        var passwordInput = document.getElementById("userPassword");
        var passwordIcon = document.getElementById("passwordIcon");

        if (passwordInput.type === "password") {
            passwordInput.type = "text";
            passwordIcon.classList.remove("fa-eye");
            passwordIcon.classList.add("fa-eye-slash");
        } else {
            passwordInput.type = "password";
            passwordIcon.classList.remove("fa-eye-slash");
            passwordIcon.classList.add("fa-eye");
        }
    }
</script> -->
<?php $__env->stopPush(); ?>
<?php echo $__env->make('theme.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\xampp\htdocs\git\oka_paper\resources\views/dashboard/admin/user-management/create.blade.php ENDPATH**/ ?>