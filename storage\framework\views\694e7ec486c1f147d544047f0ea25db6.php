<?php $__env->startSection('title','Create Supplier'); ?>
<?php $__env->startPush('css'); ?>
<style>
</style>
<link href="https://cdn.jsdelivr.net/npm/select2@4.0.13/dist/css/select2.min.css" rel="stylesheet" />
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">

<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<section class="create-new-supplier">
    <div class="container-fluid">
        <div class="row">
            <div class="col-lg-12 my-4">
                <div class="header-section d-flex justify-content-between align-items-center">
                    <h1 class="font-bold steel-blue">CREATE NEW SUPPLIER</h1>
                    <div class="cancel-save-btn d-flex gap-5">
                        <a href="" class="btn-cancel rich-black">Cancel</a>
                        <a href="" class="btn-save fs-14 text-white border-radius-10">Save</a>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-12 supplier-form py-5">
                <div class="row">
                    <div class="col-lg-6">
                        <form action="" method="POST">
                            <?php echo csrf_field(); ?>
                            <div class="form-section">
                                <h5>GENERAL INFO</h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <label for="supplierCode " class="shadowed-steel fs-16 fw-500 mb-2">Supplier Code *</label>
                                        <input type="text" name="supplier_code" id="supplierCode" class="form-control" placeholder="Enter supplier code">
                                    </div>
                                    <div class="col-md-6">
                                        <label for="shortCompanyName" class="shadowed-steel fs-16 fw-500 mb-2">Short Company Name *</label>
                                        <input type="text" name="short_company_name" id="shortCompanyName" class="form-control" placeholder="Enter short company name">
                                    </div>
                                    <div class="col-md-12">
                                        <label for="fullCompanyName" class="shadowed-steel fs-16 fw-500 mb-2">Full Company Name *</label>
                                        <input type="text" name="full_company_name" id="fullCompanyName" class="form-control" placeholder="Enter company name">
                                    </div>
                                    <div class="col-md-6">
                                        <label for="cifNif" class="shadowed-steel fs-16 fw-500 mb-2">CIF/NIF or other international</label>
                                        <input type="text" name="cif_nif" id="cifNif" class="form-control" placeholder="Enter CIF/NIF">
                                    </div>
                                    <div class="col-md-6">
                                        <label for="country" class="shadowed-steel fs-16 fw-500 mb-2">Country *</label>
                                        <select name="country" id="country" class="form-select select2-country">
                                            <option value="" selected>Select Country</option>
                                            <option value="pakistan" data-flag="🇵🇰">Pakistan</option>
                                            <option value="usa" data-flag="🇺🇸">USA</option>
                                            <option value="spain" data-flag="🇪🇸">Spain</option>
                                            <option value="china" data-flag="🇨🇳">China</option>
                                            <option value="germany" data-flag="🇩🇪">Germany</option>
                                        </select>
                                    </div>
                                    <div class="col-md-12">
                                        <label for="address" class="shadowed-steel fs-16 fw-500 mb-2"> Address *</label>
                                        <input type="text" name="address" id="address" class="form-control" placeholder="Enter full address">
                                    </div>
                                    <div class="col-md-12">
                                        <label for="website" class="shadowed-steel fs-16 fw-500 mb-2"> Website *</label>
                                        <input type="text" name="website" id="website" class="form-control" placeholder="Enter website URL">
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>

                    <div class="col-lg-6 logistics-commercial">
                        <div class="form-section">
                            <h5>LOGISTICS & CUSTOMS INFO</h5>
                            <div class="usual-ports d-flex flex-column gap-5">
                                <label for="departurePorts" class="shadowed-steel fs-16 fw-500 mb-2"> Usual Ports of Departure</label>
                                <div class="input-group">
                                    <select name="ports_of_departure[]" id="departurePorts" class="form-select select2" multiple="multiple">
                                        <option value="Shekou">Shekou</option>
                                        <option value="Nansha">Nansha</option>
                                        <option value="Xiamen">Xiamen</option>
                                        <option value="Shanghai">Shanghai</option>
                                    </select>
                                    <div id="selectedPorts" class="mt-2"></div>
                                </div>
                            </div>
                        </div>
                        <div class="form-section">
                            <h5>COMMERCIAL CONDITIONS</h5>
                            <label for="paymentMethod" class="shadowed-steel fs-16 fw-500 mb-2"> Preferred Payment Method *</label>
                            <input type="text" name="preferred_payment_method" id="paymentMethod" class="form-control" placeholder="Enter payment method">
                            <label for="currency" class="shadowed-steel fs-16 fw-500 mb-2">Currency *</label>
                            <select name="currency" id="currency" class="form-select">
                                <option value="USD">USD</option>
                                <option value="EUR">EUR</option>
                                <option value="PKR">PKR</option>
                                <option value="GBP">GBP</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <div class="text-end">
                <button type="submit" class="btn-save">Save</button>
            </div>
        </div>

        <div class="contacts mt-10">
            <div class="row">
                <div class="col-lg-12 my-4">
                    <div class="contact-header d-flex justify-content-between align-items-center">
                        <h2 class="font-bold steel-blue ">CONTACTS</h2>
                        <a href="" class="fs-16 copper-text add-contact">+ Add Another Contact</a>
                    </div>
                </div>
            </div>

            <div class="row contact-main mx-1 mt-3">
                <div class="col-lg-6">
                    <div class="contact-info d-flex flex-column gap-5">
                        <div class="form-section">
                            <label for="contactPerson" class="shadowed-steel fs-16 fw-500 mb-2">Main Contact Person Name</label>
                            <select name="contact_person" id="contact-person" class="form-select">
                                <option value="" disabled selected>Select contact person name</option>
                                <option value="tester">Tester</option>
                                <option value="user">User</option>
                                <option value="developer">Developer</option>
                            </select>
                        </div>
                        <div class="form-section">
                            <label for="phone" class="shadowed-steel fs-16 fw-500 mb-2">Phone Number</label>
                            <input type="number" name="phone" id="phone" class="form-control" placeholder="Enter phone number">
                        </div>
                    </div>
                </div>

                <div class="col-lg-6">
                    <div class="d-flex flex-column gap-5">
                        <div class="form-section">
                            <label for="email" class="shadowed-steel fs-16 fw-500 mb-2">Email</label>
                            <input type="text" name="email" id="email" class="form-control" placeholder="Enter email address">
                        </div>
                        <div class="form-section d-flex flex-column">
                            <label for="preferredChannel" class="shadowed-steel fs-16 fw-500 mb-2">Preferred Channel</label>
                            <div class="preferred-channel-options">
                                <div class="channel-option">
                                    <input type="radio" name="channel" id="whatsapp" value="whatsapp" class="channel-radio">
                                    <label for="whatsapp" class="channel-label">
                                        <div class="custom-checkbox">
                                            <i class="fas fa-check"></i>
                                        </div>
                                        <div class="app-icon whatsapp-bg">
                                            <img src="<?php echo e(asset('website/assets/media/images/whatsapp-icon.svg')); ?>" alt="WhatsApp">
                                        </div>
                                        <span class="app-name">WhatsApp</span>
                                    </label>
                                </div>
                                <div class="channel-option">
                                    <input type="radio" name="channel" id="wechat" value="wechat" class="channel-radio">
                                    <label for="wechat" class="channel-label">
                                        <div class="custom-checkbox">
                                            <i class="fas fa-check"></i>
                                        </div>
                                        <div class="app-icon wechat-bg">
                                            <img src="<?php echo e(asset('website/assets/media/images/wechat_symbol.svg.svg')); ?>" alt="WeChat">
                                        </div>
                                        <span class="app-name">WeChat</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('js'); ?>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/select2@4.0.13/dist/js/select2.min.js"></script>

<script>
    $(document).ready(function() {
        $('.select2').select2({
            placeholder: 'Add port (e.g., Shanghai)',
            allowClear: true
        });

        $('#departurePorts').on('change', function() {
            var selectedOptions = $(this).val();
            var selectedPortsDiv = $('#selectedPorts');
            selectedPortsDiv.empty();

            selectedOptions.forEach(function(port) {
                var portElement = $('<span class="badge badge-pill badge-primary m-1">' + port + ' <span class="remove-port" style="cursor:pointer;">&times;</span></span>');
                selectedPortsDiv.append(portElement);
            });

            $('.remove-port').on('click', function() {
                var portText = $(this).parent().text().trim();
                $(this).parent().remove();
                var options = $('#departurePorts').val();
                options = options.filter(function(value) {
                    return value !== portText;
                });
                $('#departurePorts').val(options).trigger('change');
            });
        });
    });
</script>

<script>
    $(document).ready(function () {
    var contactCounter = 1;

    $(".add-contact").on("click", function (e) {
        e.preventDefault();

        var newContactForm = $(".contact-main").first().clone();

        newContactForm.find("input, select").val("");

        // Add remove button to new contact form
        newContactForm.prepend('<i class="fas fa-trash-alt remove-btn"></i>');

        // Make unique names for radio buttons
        var uniqueChannelName = "channel_" + contactCounter;
        newContactForm.find('input[name="channel"]').attr('name', uniqueChannelName);

        // Update IDs to be unique
        newContactForm.find('#whatsapp').attr('id', 'whatsapp_' + contactCounter);
        newContactForm.find('#wechat').attr('id', 'wechat_' + contactCounter);

        // Update labels to match new IDs
        newContactForm.find('label[for="whatsapp"]').attr('for', 'whatsapp_' + contactCounter);
        newContactForm.find('label[for="wechat"]').attr('for', 'wechat_' + contactCounter);

        newContactForm.addClass("mt-10");

        $(".contacts").append(newContactForm);

        contactCounter++;
    });

    $(document).on("click", ".remove-btn", function () {
        $(this).closest(".contact-main").remove();
    });
});

</script>

<!-- <script>
    $(document).ready(function() {
        var countries = [{
                code: 'PK',
                name: 'Pakistan'
            },
            {
                code: 'US',
                name: 'USA'
            },
            {
                code: 'ES',
                name: 'Spain'
            },
            {
                code: 'CN',
                name: 'China'
            },
            {
                code: 'DE',
                name: 'Germany'
            }
        ];

        var $countryDropdown = $('#country');

        $.each(countries, function(index, country) {
            $countryDropdown.append(
                $('<option>', {
                    value: country.code,
                    text: country.name,
                    'data-flag': country.code.toLowerCase()
                })
            );
        });

        $countryDropdown.on('change', function() {
            var selectedCode = $(this).val();
            var selectedFlag = 'flag-icon-' + selectedCode.toLowerCase();
            var selectedText = $(this).find("option:selected").text();

            $(this).next('.flag-container').remove();
            if (selectedCode) {
                $(this).after('<span class="flag-icon ' + selectedFlag + ' flag-container"></span>');
            }
        });
    });
</script> -->
<?php $__env->stopPush(); ?>
<?php echo $__env->make('theme.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\xampp\htdocs\git\oka_paper\resources\views/dashboard/admin/previous-warehouse/suppliers/create.blade.php ENDPATH**/ ?>