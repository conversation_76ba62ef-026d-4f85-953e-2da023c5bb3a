<?php $__env->startSection('title','Create Supplier'); ?>
<?php $__env->startPush('css'); ?>
<style>
    .create-new-supplier {
        padding: 30px 0;
    }

    .header-section h1 {
        font-size: 26px;
        color: #303f63;
        margin-bottom: 20px;
    }

    .cancel-save-btn a {
        padding: 10px 20px;
        font-size: 14px;
        color: #ffffff;
        background-color: #ff7b4d;
        border-radius: 10px;
        text-decoration: none;
    }

    .cancel-save-btn .btn-cancel {
        background-color: #6c757d;
    }

    .supplier-form .form-section,
    .contacts {
        padding: 20px;
        border-radius: 12px;
        border: 1px solid #E7E7E7;
        background: rgba(255, 255, 255, 0.33);
        backdrop-filter: blur(22.5px);
    }

    .supplier-form .form-section h5 {
        font-size: 18px;
        color: #303f63;
        margin-bottom: 20px;
        font-weight: 600;
    }

    .supplier-form .form-control {
        font-size: 14px;
        padding: 10px;
        border-radius: 5px;
        border: 1px solid #ccc;
        margin-bottom: 20px;
        box-sizing: border-box;
    }

    .supplier-form select.form-control {
        height: 45px;
        font-size: 14px;
    }

    .supplier-form .badge {
        background-color: #007bff;
        color: #fff;
        padding: 5px 10px;
        border-radius: 20px;
        margin-right: 5px;
    }

    .supplier-form .badge span.remove-port {
        cursor: pointer;
        color: #fff;
        margin-left: 10px;
    }

    .btn-save {
        padding: 10px 25px;
        font-size: 16px;
        color: #fff;
        background-color: #007bff;
        border-radius: 5px;
        border: none;
    }

    .btn-save:hover {
        background-color: #0056b3;
    }

    .logistics-commercial {
        display: flex;
        flex-direction: column;
        gap: 30px;
    }

    .usual-ports .input-group {
        border-radius: 12px;
        border: 1px solid #EEE;
        background: #FAFAFA;
        padding: 20px;
    }

    /* Preferred Channel Styles */
    .preferred-channel-options {
        display: flex;
        gap: 15px;
        margin-top: 10px;
    }

    .channel-option {
        position: relative;
        flex: 1;
    }

    .channel-radio {
        display: none;
    }

    .channel-label {
        display: block;
        cursor: pointer;
        border: 2px solid #E7E7E7;
        border-radius: 12px;
        padding: 20px;
        background: #FFFFFF;
        transition: all 0.3s ease;
        position: relative;
        height: 100%;
    }

    .channel-label:hover {
        border-color: #007bff;
        box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);
    }

    .channel-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
        position: relative;
    }

    .channel-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 10px;
        font-size: 24px;
        color: white;
    }

    .whatsapp-icon {
        background: #25D366;
    }

    .wechat-icon {
        background: #1AAD19;
    }

    .channel-name {
        font-size: 14px;
        font-weight: 500;
        color: #303f63;
    }

    .check-mark {
        position: absolute;
        top: -10px;
        right: -10px;
        width: 24px;
        height: 24px;
        background: #28a745;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 12px;
        opacity: 0;
        transform: scale(0);
        transition: all 0.3s ease;
    }

    .channel-radio:checked + .channel-label {
        border-color: #28a745;
        background: #f8fff9;
        box-shadow: 0 4px 12px rgba(40, 167, 69, 0.2);
    }

    .channel-radio:checked + .channel-label .check-mark {
        opacity: 1;
        transform: scale(1);
    }
</style>
<link href="https://cdn.jsdelivr.net/npm/select2@4.0.13/dist/css/select2.min.css" rel="stylesheet" />
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">


<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<section class="create-new-supplier">
    <div class="container-fluid">
        <div class="row">
            <div class="col-lg-12 my-4">
                <div class="header-section d-flex justify-content-between align-items-center">
                    <h1 class="font-bold steel-blue">CREATE NEW SUPPLIER</h1>
                    <div class="cancel-save-btn d-flex gap-5">
                        <a href="" class="btn-cancel rich-black">Cancel</a>
                        <a href="" class="btn-save fs-14 text-white border-radius-10">Save</a>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-12 supplier-form py-5">
                <div class="row">
                    <div class="col-lg-6">
                        <form action="" method="POST">
                            <?php echo csrf_field(); ?>
                            <div class="form-section">
                                <h5>GENERAL INFO</h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <label for="supplierCode " class="shadowed-steel fs-16 fw-500 mb-2">Supplier Code *</label>
                                        <input type="text" name="supplier_code" id="supplierCode" class="form-control" placeholder="Enter supplier code">
                                    </div>
                                    <div class="col-md-6">
                                        <label for="shortCompanyName" class="shadowed-steel fs-16 fw-500 mb-2">Short Company Name *</label>
                                        <input type="text" name="short_company_name" id="shortCompanyName" class="form-control" placeholder="Enter short company name">
                                    </div>
                                    <div class="col-md-12">
                                        <label for="fullCompanyName" class="shadowed-steel fs-16 fw-500 mb-2">Full Company Name *</label>
                                        <input type="text" name="full_company_name" id="fullCompanyName" class="form-control" placeholder="Enter company name">
                                    </div>
                                    <div class="col-md-6">
                                        <label for="cifNif" class="shadowed-steel fs-16 fw-500 mb-2">CIF/NIF or other international</label>
                                        <input type="text" name="cif_nif" id="cifNif" class="form-control" placeholder="Enter CIF/NIF">
                                    </div>
                                    <div class="col-md-6">
                                        <label for="country" class="shadowed-steel fs-16 fw-500 mb-2">Country *</label>
                                        <select name="country" id="country" class="form-select">
                                            <option value="" selected>Select Country</option>
                                            <option value="pakistan">Pakistan</option>
                                            <option value="usa">USA</option>
                                            <option value="spain">Spain</option>
                                            <option value="china">China</option>
                                            <option value="germany">Germany</option>
                                        </select>
                                    </div>
                                    <div class="col-md-12">
                                        <label for="address" class="shadowed-steel fs-16 fw-500 mb-2"> Address *</label>
                                        <input type="text" name="address" id="address" class="form-control" placeholder="Enter full address">
                                    </div>
                                    <div class="col-md-12">
                                        <label for="website" class="shadowed-steel fs-16 fw-500 mb-2"> Website *</label>
                                        <input type="text" name="website" id="website" class="form-control" placeholder="Enter website URL">
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>

                    <div class="col-lg-6 logistics-commercial">
                        <div class="form-section">
                            <h5>LOGISTICS & CUSTOMS INFO</h5>
                            <div class="usual-ports d-flex flex-column gap-5">
                                <label for="departurePorts" class="shadowed-steel fs-16 fw-500 mb-2"> Usual Ports of Departure</label>
                                <div class="input-group">
                                    <select name="ports_of_departure[]" id="departurePorts" class="form-select select2" multiple="multiple">
                                        <option value="Shekou">Shekou</option>
                                        <option value="Nansha">Nansha</option>
                                        <option value="Xiamen">Xiamen</option>
                                        <option value="Shanghai">Shanghai</option>
                                    </select>
                                    <div id="selectedPorts" class="mt-2"></div>
                                </div>
                            </div>
                        </div>
                        <div class="form-section">
                            <h5>COMMERCIAL CONDITIONS</h5>
                            <label for="paymentMethod" class="shadowed-steel fs-16 fw-500 mb-2"> Preferred Payment Method *</label>
                            <input type="text" name="preferred_payment_method" id="paymentMethod" class="form-control" placeholder="Enter payment method">
                            <label for="currency" class="shadowed-steel fs-16 fw-500 mb-2">Currency *</label>
                            <select name="currency" id="currency" class="form-select">
                                <option value="USD">USD</option>
                                <option value="EUR">EUR</option>
                                <option value="PKR">PKR</option>
                                <option value="GBP">GBP</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <div class="text-end">
                <button type="submit" class="btn-save">Save</button>
            </div>
        </div>

        <div class="contacts mt-10">
            <div class="row">
                <div class="col-lg-12 my-4">
                    <div class="contact-header d-flex justify-content-between align-items-center">
                        <h3 class="font-bold steel-blue fs-18">CONTACTS</h3>
                        <a href="" class="fs-16 copper-text">+ Add Another Contact</a>
                    </div>
                </div>

                <div class="col-lg-6">
                    <div class="contact-info d-flex flex-column gap-5">
                        <div class="form-section">
                            <label for="contactPerson" class="shadowed-steel fs-16 fw-500 mb-2">Main Contact Person Name</label>
                            <select name="contact_person" id="contact-person" class="form-select">
                                <option value="" disabled selected>Select contact person name</option>
                                <option value="tester">Tester</option>
                                <option value="user">User</option>
                                <option value="developer">Developer</option>
                            </select>
                        </div>
                        <div class="form-section">
                            <label for="phone" class="shadowed-steel fs-16 fw-500 mb-2">Phone Number</label>
                            <input type="number" name="phone" id="phone" class="form-control" placeholder="Enter phone number">
                        </div>
                    </div>
                </div>

                <div class="col-lg-6">
                    <div class="d-flex flex-column gap-5">
                        <div class="form-section">
                            <label for="email" class="shadowed-steel fs-16 fw-500 mb-2">Email</label>
                            <input type="text" name="email" id="email" class="form-control" placeholder="Enter email address">
                        </div>
                        <div class="form-section d-flex flex-column">
                            <label for="preferredChannel" class="shadowed-steel fs-16 fw-500 mb-2">Preferred Channel</label>
                            <div class="preferred-channel-options">
                                <div class="channel-option">
                                    <input type="radio" name="channel" id="whatsapp" value="whatsapp" class="channel-radio">
                                    <label for="whatsapp" class="channel-label">
                                        <div class="channel-content">
                                            <div class="channel-icon whatsapp-icon">
                                                <i class="fab fa-whatsapp"></i>
                                            </div>
                                            <span class="channel-name">WhatsApp</span>
                                            <div class="check-mark">
                                                <i class="fas fa-check"></i>
                                            </div>
                                        </div>
                                    </label>
                                </div>
                                <div class="channel-option">
                                    <input type="radio" name="channel" id="wechat" value="wechat" class="channel-radio">
                                    <label for="wechat" class="channel-label">
                                        <div class="channel-content">
                                            <div class="channel-icon wechat-icon">
                                                <i class="fab fa-weixin"></i>
                                            </div>
                                            <span class="channel-name">WeChat</span>
                                            <div class="check-mark">
                                                <i class="fas fa-check"></i>
                                            </div>
                                        </div>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('js'); ?>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/select2@4.0.13/dist/js/select2.min.js"></script>

<script>
    $(document).ready(function() {
        $('.select2').select2({
            placeholder: 'Add port (e.g., Shanghai)',
            allowClear: true
        });

        $('#departurePorts').on('change', function() {
            var selectedOptions = $(this).val();
            var selectedPortsDiv = $('#selectedPorts');
            selectedPortsDiv.empty();

            selectedOptions.forEach(function(port) {
                var portElement = $('<span class="badge badge-pill badge-primary m-1">' + port + ' <span class="remove-port" style="cursor:pointer;">&times;</span></span>');
                selectedPortsDiv.append(portElement);
            });

            $('.remove-port').on('click', function() {
                var portText = $(this).parent().text().trim();
                $(this).parent().remove();
                var options = $('#departurePorts').val();
                options = options.filter(function(value) {
                    return value !== portText;
                });
                $('#departurePorts').val(options).trigger('change');
            });
        });
    });
</script>

<!-- <script>
    $(document).ready(function() {
        var countries = [{
                code: 'PK',
                name: 'Pakistan'
            },
            {
                code: 'US',
                name: 'USA'
            },
            {
                code: 'ES',
                name: 'Spain'
            },
            {
                code: 'CN',
                name: 'China'
            },
            {
                code: 'DE',
                name: 'Germany'
            }
        ];

        var $countryDropdown = $('#country');

        $.each(countries, function(index, country) {
            $countryDropdown.append(
                $('<option>', {
                    value: country.code,
                    text: country.name,
                    'data-flag': country.code.toLowerCase()
                })
            );
        });

        $countryDropdown.on('change', function() {
            var selectedCode = $(this).val();
            var selectedFlag = 'flag-icon-' + selectedCode.toLowerCase();
            var selectedText = $(this).find("option:selected").text();

            $(this).next('.flag-container').remove();
            if (selectedCode) {
                $(this).after('<span class="flag-icon ' + selectedFlag + ' flag-container"></span>');
            }
        });
    });
</script> -->
<?php $__env->stopPush(); ?>
<?php echo $__env->make('theme.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\xampp\htdocs\git\oka_paper\resources\views/dashboard/admin/previous-warehouse/suppliers/create.blade.php ENDPATH**/ ?>