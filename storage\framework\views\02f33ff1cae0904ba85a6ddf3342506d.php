<div class="offcanvas offcanvas-end" tabindex="-1" id="uploadDocumentsOffcanvas" aria-labelledby="uploadDocumentsOffcanvasLabel">
    <div class="offcanvas-header">
        <h5 class="offcanvas-title fs-16 fw-600" id="uploadDocumentsOffcanvasLabel">Upload Documents</h5>
        <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
    </div>
    <div class="offcanvas-body">
        <form class="upload-documents-form h-100 d-flex flex-column justify-content-between">
            <div class="upload-fields">
                <div class="mb-3">
                    <label for="invoiceNumber" class="form-label shadowed-steel fs-16 fw-500 mb-2">Invoice #</label>
                    <div class="input-group">
                        <input type="text" class="form-control" id="invoiceNumber" value="INV-0014" readonly>
                    </div>
                </div>

                <div class="mb-3">
                    <label class="form-label shadowed-steel fs-16 fw-500 mb-2">Upload Commercial Invoice</label>
                    <input type="file" class="form-control file-input" id="commercialInvoice" data-target="commercial-invoice">
                    <div class="no-file-text text-muted small mt-1">No file choose</div>

                </div>

                <div class="mb-3">
                    <label class="form-label shadowed-steel fs-16 fw-500 mb-2">Upload Transfer Receipt</label>
                    <input type="file" class="form-control file-input" data-target="transfer-receipt">
                    <div class="no-file-text text-muted small mt-1">No file choose</div>

                    <div class="uploaded-files mt-2" id="transfer-receipt-files">
                    </div>
                </div>

                <div class="mb-3">
                    <label class="form-label shadowed-steel fs-16 fw-500 mb-2">Upload Packing List</label>
                    <input type="file" class="form-control file-input" data-target="packing-list">
                    <div class="no-file-text text-muted small mt-1">No file choose</div>

                    <div class="uploaded-files mt-2" id="packing-list-files">
                    </div>
                </div>

                <div class="mb-4">
                    <label class="form-label shadowed-steel fs-16 fw-500 mb-2">Upload Freight Cost</label>
                    <input type="file" class="form-control file-input" data-target="freight-cost">
                    <div class="no-file-text text-muted small mt-1">No file choose</div>

                    <div class="uploaded-files mt-2" id="freight-cost-files">
                    </div>
                </div>
            </div>

            <div class="row g-2 mt-4">
                <div class="col">
                    <button type="button" class="btn btn-outline-secondary w-100 cancel-btn" data-bs-dismiss="offcanvas">
                        Cancel
                    </button>
                </div>
                <div class="col">
                    <button type="submit" class="btn btn-warning text-white d-block w-100 btn-save">
                        Save
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    document.querySelectorAll('.uploaded-files').forEach(function(container) {
        const noFileText = container.parentElement.querySelector('.no-file-text');
        if (container.children.length > 0) {
            noFileText.style.display = 'none';
        }
    });

    document.querySelectorAll('.file-input').forEach(function(input) {
        input.addEventListener('change', function() {
            const target = this.getAttribute('data-target');
            const filesContainer = document.getElementById(target + '-files');
            const noFileText = this.parentElement.querySelector('.no-file-text');

            if (this.files && this.files.length > 0) {
                noFileText.style.display = 'none';
                const fileName = this.files[0].name;
                const fileElement = document.createElement('div');
                fileElement.className = 'd-flex justify-content-between align-items-center mb-2';
                fileElement.innerHTML = `
                    <span class="text-muted small">${fileName}</span>
                    <button type="button" class="btn btn-sm text-danger border-0 bg-transparent remove-file">
                        <i class="fas fa-trash trash-btn"></i>
                    </button>
                `;

                filesContainer.appendChild(fileElement);

                this.value = '';
            }
        });
    });

    document.addEventListener('click', function(e) {
        if (e.target.closest('.remove-file')) {
            const fileElement = e.target.closest('.d-flex');
            const filesContainer = fileElement.parentElement;
            const noFileText = filesContainer.parentElement.querySelector('.no-file-text');

            fileElement.remove();

            if (filesContainer.children.length === 0) {
                noFileText.style.display = 'block';
            }
        }
    });
});
</script><?php /**PATH D:\xampp\htdocs\git\oka_paper\resources\views/dashboard/templates/off-canvas/upload-document.blade.php ENDPATH**/ ?>