<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\View;

class ThemeController extends Controller{
    function __construct()
    {
         
    }
<<<<<<< Updated upstream
    public function dashboard(){
    	return view('dashboard.templates.admin-index');
    }
    public function permissions(){
    	return view('theme.user-management.permissions');
    }
    
    public function userManagement(){
    	return view('dashboard.admin.user-management');
    }
    public function createNewUser(){
    	return view('dashboard.admin.create-new-user');
    }
    public function suppliers(){
        return view('dashboard.admin.previous-warehouse.suppliers');
    }
    public function createSupplier(){
        return view('dashboard.admin.previous-warehouse.create-supplier');
    }
=======

>>>>>>> Stashed changes

    
    
}
