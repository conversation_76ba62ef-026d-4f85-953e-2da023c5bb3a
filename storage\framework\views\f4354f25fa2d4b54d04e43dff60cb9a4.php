<?php $__env->startSection('title','User Management'); ?>
<?php $__env->startPush('css'); ?>
<style>

</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<section class="tab-table-sec">
    <div class="container">
        <div class="row">
            <div class="col-lg-12 p-0 my-4">
                <!-- Header Section -->
                <div class="header-section">
                    <div class="d-flex justify-content-between align-items-center">
                        <h1 class="font-bold steel-blue">USER MANAGEMENT</h1>
                        <div class="header-controls d-flex align-items-center gap-4">
                            <div class="custom-search">
                                <i class="fas fa-search"></i>
                                <input type="text" id="customSearch" placeholder="Search">
                            </div>
                            <div class="dropdown filter-dropdown">
                                <button class="btn-filters fs-14" data-bs-toggle="dropdown" aria-expanded="false">
                                    <img src="<?php echo e(asset('website/assets/media/images/filters-lines-icon.svg')); ?>" alt="Filter" class="img-fluid" width="20" height="20">
                                    Filters
                                </button>
                                <div class="dropdown-menu filter-dropdown-menu">
                                    <h6 class="dropdown-header">Filter by Status</h6>
                                    <div class="dropdown-item">
                                        <div class="form-check">
                                            <input class="form-check-input filter-check" type="checkbox" value="active" id="filterActive">
                                            <label class="form-check-label" for="filterActive">
                                                Active
                                            </label>
                                        </div>
                                    </div>
                                    <div class="dropdown-item">
                                        <div class="form-check">
                                            <input class="form-check-input filter-check" type="checkbox" value="inactive" id="filterInactive">
                                            <label class="form-check-label" for="filterInactive">
                                                Inactive
                                            </label>
                                        </div>
                                    </div>

                                    <div class="dropdown-divider"></div>

                                    <h6 class="dropdown-header">Filter by Role</h6>
                                    <div class="dropdown-item">
                                        <div class="form-check">
                                            <input class="form-check-input filter-check" type="checkbox" value="Admin Employees" id="filterAdmin">
                                            <label class="form-check-label" for="filterAdmin">
                                                Admin Employees
                                            </label>
                                        </div>
                                    </div>
                                    <div class="dropdown-item">
                                        <div class="form-check">
                                            <input class="form-check-input filter-check" type="checkbox" value="Workers" id="filterWorkers">
                                            <label class="form-check-label" for="filterWorkers">
                                                Workers
                                            </label>
                                        </div>
                                    </div>
                                    <div class="dropdown-item">
                                        <div class="form-check">
                                            <input class="form-check-input filter-check" type="checkbox" value="Commercial Employee" id="filterCommercial">
                                            <label class="form-check-label" for="filterCommercial">
                                                Commercial Employees
                                            </label>
                                        </div>
                                    </div>

                                    <div class="dropdown-divider"></div>

                                    <div class="dropdown-item">
                                        <button class="btn btn-sm btn-primary w-100" id="applyFilters">Apply Filters</button>
                                    </div>
                                </div>
                            </div>
                            <a href="<?php echo e(route('create-new-user')); ?>" class="btn-create fs-14 text-white border-radius-5">
                                <i class="fas fa-plus text-white"></i>
                                Create New User
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-5">
                <!-- Tabs Section -->

                <div class="tabs-section fit-content">
                    <ul class="nav nav-pills" id="pills-tab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="pills-home-tab" data-bs-toggle="pill" data-bs-target="#pills-home" type="button" role="tab" aria-controls="pills-home" aria-selected="true">Admin Employees</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="pills-profile-tab" data-bs-toggle="pill" data-bs-target="#pills-profile" type="button" role="tab" aria-controls="pills-profile" aria-selected="false">Workers</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="pills-contact-tab" data-bs-toggle="pill" data-bs-target="#pills-contact" type="button" role="tab" aria-controls="pills-contact" aria-selected="false">Commercial Employees</button>
                        </li>
                    </ul>

                </div>
            </div>
            <div class="col-lg-12">
                <div class="main-container mt-10">
                    <!-- Table Section -->
                    <div class="tab-content" id="pills-tabContent">
                        <div class="tab-pane fade show active" id="pills-home" role="tabpanel" aria-labelledby="pills-home-tab">
                            <div class="table-container">
                                <table id="userTable" class="table table-striped table-bordered user-table" style="width:100%">
                                    <thead>
                                        <tr>
                                            <th>NAME</th>
                                            <th>EMAIL</th>
                                            <th>ROLE</th>
                                            <th>STATUS</th>
                                            <th>ACTIONS</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php for($i=0; $i<=10; $i++): ?>
                                            <tr data-role="Admin Employees">
                                            <td>
                                                <div class="user-info">
                                                    <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face" alt="Ramona Langosh" class="user-avatar">
                                                    <div>
                                                        <div class="dark-slate fs-14 fw-500">Ramona Langosh</div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="fs-14 fw-400 gunmetal-gray"><EMAIL></td>
                                            <td><span class="role-badge role-admin">Admin Employees</span></td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <label class="status-toggle">
                                                        <input type="checkbox" checked onchange="toggleStatusStatic(this)">
                                                        <span class="toggle-slider"></span>
                                                    </label>
                                                    <span class="status-text status-active">Active</span>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="action-buttons">
                                                    <a href="#">
                                                        <i class="fa-regular fa-eye"></i>
                                                    </a>
                                                    <a href="#" class="btn-edit"">
                                                <i class=" fas fa-edit"></i>
                                                    </a>
                                                </div>
                                            </td>
                                            </tr>
                                            <?php endfor; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="pills-profile" role="tabpanel" aria-labelledby="pills-profile-tab">
                            <div class="table-container">
                                <table id="userTable" class="table table-striped table-bordered user-table" style="width:100%">
                                    <thead>
                                        <tr>
                                            <th>NAME</th>
                                            <th>EMAIL</th>
                                            <th>ROLE</th>
                                            <th>STATUS</th>
                                            <th>ACTIONS</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php for($i=0; $i<=10; $i++): ?>
                                            <tr data-role="Workers">
                                            <td>
                                                <div class="user-info">
                                                    <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face" alt="Travis Mraz" class="user-avatar">
                                                    <div>
                                                        <div class="dark-slate fs-14 fw-500">Travis Mraz</div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="fs-14 fw-400 gunmetal-gray"><EMAIL></td>
                                            <td><span class="role-badge role-worker">Workers</span></td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <label class="status-toggle">
                                                        <input type="checkbox" onchange="toggleStatusStatic(this)">
                                                        <span class="toggle-slider"></span>
                                                    </label>
                                                    <span class="status-text status-inactive">Deactive</span>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="action-buttons">
                                                    <a href="#">
                                                        <i class="fa-regular fa-eye"></i>
                                                    </a>
                                                    <a href="#" class="btn-edit" "="">
                                                        <i class=" fa-solid fa-trash"></i>
                                                    </a>
                                                </div>
                                            </td>
                                            </tr>
                                            <?php endfor; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="pills-contact" role="tabpanel" aria-labelledby="pills-contact-tab">
                            <div class="table-container">
                                <table id="userTable" class="table table-striped table-bordered user-table" style="width:100%">
                                    <thead>
                                        <tr>
                                            <th>NAME</th>
                                            <th>EMAIL</th>
                                            <th>ROLE</th>
                                            <th>STATUS</th>
                                            <th>ACTIONS</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php for($i=0; $i<=10; $i++): ?>
                                            <tr data-role="Commercial Employee">
                                            <td>
                                                <div class="user-info">
                                                    <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face" alt="Claire Hauck-Bashirian" class="user-avatar">
                                                    <div>
                                                        <div class="dark-slate fs-14 fw-500">Claire Hauck-Bashirian</div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="fs-14 fw-400 gunmetal-gray"><EMAIL></td>
                                            <td><span class="role-badge role-commercial">Commercial Employee</span></td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <label class="status-toggle">
                                                        <input type="checkbox" onchange="toggleStatusStatic(this)">
                                                        <span class="toggle-slider"></span>
                                                    </label>
                                                    <span class="status-text status-inactive">Deactive</span>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="action-buttons">
                                                <a href="#">
                                                        <i class="fa-regular fa-eye"></i>
                                                    </a>
                                                    <a href="#" class="btn-edit" "="">
                                                        <i class=" fa-solid fa-trash"></i>
                                                    </a>
                                                </div>
                                            </td>
                                            </tr>
                                            <?php endfor; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
</section>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('js'); ?>



<?php $__env->stopPush(); ?>
<?php echo $__env->make('theme.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\xampp\htdocs\git\oka_paper\resources\views/dashboard/admin/user-management/index.blade.php ENDPATH**/ ?>