<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Models\Crud;
use Illuminate\Support\Facades\View;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        //
        if (\Schema::hasTable('cruds')) {
        $crud = Crud::get();
        view()->share('crud', $crud);
    }    

    }
}
