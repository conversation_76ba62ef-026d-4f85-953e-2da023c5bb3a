{"name": "spatie/laravel-permission", "description": "Permission handling for Laravel 6.0 and up", "license": "MIT", "keywords": ["spatie", "laravel", "permission", "permissions", "roles", "acl", "rbac", "security"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://spatie.be", "role": "Developer"}], "homepage": "https://github.com/spatie/laravel-permission", "require": {"php": "^7.3|^8.0", "illuminate/auth": "^7.0|^8.0|^9.0|^10.0", "illuminate/container": "^7.0|^8.0|^9.0|^10.0", "illuminate/contracts": "^7.0|^8.0|^9.0|^10.0", "illuminate/database": "^7.0|^8.0|^9.0|^10.0"}, "require-dev": {"orchestra/testbench": "^5.0|^6.0|^7.0|^8.0", "phpunit/phpunit": "^9.4", "predis/predis": "^1.1"}, "minimum-stability": "dev", "prefer-stable": true, "autoload": {"psr-4": {"Spatie\\Permission\\": "src"}, "files": ["src/helpers.php"]}, "autoload-dev": {"psr-4": {"Spatie\\Permission\\Test\\": "tests"}}, "config": {"sort-packages": true}, "extra": {"branch-alias": {"dev-main": "5.x-dev", "dev-master": "5.x-dev"}, "laravel": {"providers": ["Spatie\\Permission\\PermissionServiceProvider"]}}, "scripts": {"test": "phpunit"}}