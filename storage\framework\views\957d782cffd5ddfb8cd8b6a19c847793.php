<!DOCTYPE html>
<html lang="en">
<!--begin::Head-->

<head>
    <base href="" />
    <title><?php echo $__env->yieldContent('title'); ?></title>
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <meta charset="utf-8" />
    <meta name="description" content="" />
    <meta name="keywords" content="" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta property="og:locale" content="en_US" />
    <meta property="og:type" content="article" />
    <meta property="og:title" content="" />
    <meta property="og:url" content="https://keenthemes.com/metronic" />
    <meta property="og:site_name" content="Keenthemes | Metronic" />
    <link rel="canonical" href="https://preview.keenthemes.com/metronic8" />
    <link rel="shortcut icon" href="<?php echo e(asset('')); ?><?php echo e(App\Models\Setting::first()->favicon??''); ?>" />
    <!--begin::Fonts(mandatory for all pages)-->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Inter:300,400,500,600,700" />
    <link rel="stylesheet" href="<?php echo e(asset('website')); ?>/assets/css/cabinet-grotesk-variable.css" />
    <link rel="stylesheet" href="<?php echo e(asset('website')); ?>/assets/css/cabinet-grotesk-helpers.css" />
    <!--end::Fonts-->

    <link rel="stylesheet" href="https://cdn.rawgit.com/valdrin/flagstrap/3f8485a6/dist/css/flagstrap.min.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">


    <!--begin::Global Stylesheets Bundle(mandatory for all pages)-->
    <link href="<?php echo e(asset('website')); ?>/assets/plugins/global/plugins.bundle.css" rel="stylesheet" type="text/css" />
    <link href="<?php echo e(asset('website')); ?>/assets/css/style.bundle.css" rel="stylesheet" type="text/css" />
    <link href="https:bootstrapcdn.com/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet" />
    <!-- <link href="https://cdnjs.cloudflare.com/ajax/libs/datatables/1.10.21/css/dataTables.bootstrap5.min.css" rel="stylesheet"> -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.0.13/dist/css/select2.min.css" rel="stylesheet" />

    <link href="<?php echo e(asset('website')); ?>/assets/css/dashboard.css" rel="stylesheet" type="text/css" />
    <!-- <link href="<?php echo e(asset('website')); ?>/assets/css/dashboard.css" rel="stylesheet" type="text/css" /> -->
    <!--end::Global Stylesheets Bundle-->
    <?php echo $__env->yieldPushContent('css'); ?>
</head>
<!--end::Head-->
<!--begin::Body-->

<body id="kt_app_body" data-kt-app-layout="dark-sidebar" data-kt-app-header-fixed="true"
    data-kt-app-sidebar-enabled="true" data-kt-app-sidebar-fixed="true" data-kt-app-sidebar-hoverable="true"
    data-kt-app-sidebar-push-header="true" data-kt-app-sidebar-push-toolbar="true"
    data-kt-app-sidebar-push-footer="true" data-kt-app-toolbar-enabled="true" class="app-default">
    <!--begin::Theme mode setup on page load-->
    <script>
        var defaultThemeMode = "light";
        var themeMode;
        if (document.documentElement) {
            if (document.documentElement.hasAttribute("data-bs-theme-mode")) {
                themeMode = document.documentElement.getAttribute("data-bs-theme-mode");
            } else {
                if (localStorage.getItem("data-bs-theme") !== null) {
                    themeMode = localStorage.getItem("data-bs-theme");
                } else {
                    themeMode = defaultThemeMode;
                }
            }
            if (themeMode === "system") {
                themeMode = window.matchMedia("(prefers-color-scheme: dark)").matches ? "dark" : "light";
            }
            document.documentElement.setAttribute("data-bs-theme", themeMode);
        }
    </script>
    <!--end::Theme mode setup on page load-->
    <!--begin::App-->
    <div class="d-flex flex-column flex-root app-root" id="kt_app_root">
        <!--begin::Page-->
        <div class="app-page flex-column flex-column-fluid" id="kt_app_page">
            <!--begin::Header-->
            <div id="kt_app_header" class="app-header" data-kt-sticky="true"
                data-kt-sticky-activate="{default: true, lg: true}" data-kt-sticky-name="app-header-minimize"
                data-kt-sticky-offset="{default: '200px', lg: '0'}" data-kt-sticky-animation="false">
                <!--begin::Header container-->
                <div class="app-container container-fluid d-flex align-items-stretch justify-content-between"
                    id="kt_app_header_container">
                    <!--begin::Sidebar mobile toggle-->
                    <div class="d-flex align-items-center d-lg-none ms-n3 me-1 me-md-2" title="Show sidebar menu">
                        <div class="btn btn-icon btn-active-color-primary w-35px h-35px"
                            id="kt_app_sidebar_mobile_toggle">
                            <i class="ki-duotone ki-abstract-14 fs-2 fs-md-1">
                                <span class="path1"></span>
                                <span class="path2"></span>
                            </i>
                        </div>
                    </div>
                    <div class="d-flex align-items-center flex-grow-1 flex-lg-grow-0">
                        <a href="../../demo1/dist/index.html" class="d-lg-none">
                            <img alt="Logo" src="<?php echo e(asset('website')); ?>/assets/media/logos/default-small.svg"
                                class="h-30px" />
                        </a>
                    </div>
                    <div class="d-flex align-items-stretch justify-content-between flex-lg-grow-1"
                        id="kt_app_header_wrapper">
                        <!--begin::Menu wrapper-->
                        <?php echo $__env->make('theme.layout.navbar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                        <!--end::Menu wrapper-->
                        <!--begin::Navbar-->
                        <?php echo $__env->make('theme.layout.right_sidebar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                        <!--end::Navbar-->
                    </div>
                    <!--end::Header wrapper-->
                </div>
                <!--end::Header container-->
            </div>
            <!--end::Header-->
            <!--begin::Wrapper-->
            <div class="app-wrapper flex-column flex-row-fluid" id="kt_app_wrapper">
                <?php echo $__env->make('theme.layout.sidebar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                <div class="app-main flex-column flex-row-fluid mx-10" id="kt_app_main">
                    <div class="d-flex flex-column flex-column-fluid">
                        <?php echo $__env->yieldContent('breadcrumb'); ?>
                        <?php echo $__env->yieldContent('content'); ?>
                    </div>
                    <div id="kt_app_footer" class="app-footer">
                        <div
                            class="app-container container-fluid d-flex flex-column flex-md-row flex-center flex-md-stack py-3">
                            <div class="text-dark order-2 order-md-1">
                                <span class="text-muted fw-semibold me-1"><?php echo e(App\Models\Setting::first()->footer_text??''); ?>&copy;</span>
                                <a href="https://keenthemes.com" target="_blank"
                                    class="text-gray-800 text-hover-primary">Admin</a>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
            <!--end::Wrapper-->
        </div>
        <!--end::Page-->
    </div>
    <div id="kt_scrolltop" class="scrolltop" data-kt-scrolltop="true">
        <i class="ki-duotone ki-arrow-up">
            <span class="path1"></span>
            <span class="path2"></span>
        </i>
    </div>
    <?php echo $__env->make('theme.layout.modal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <script>
        var hostUrl = "<?php echo e(asset('website')); ?>/assets/";
    </script>
    <script src="<?php echo e(asset('website')); ?>/assets/plugins/global/plugins.bundle.js"></script>
    <script src="<?php echo e(asset('website')); ?>/assets/js/scripts.bundle.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- DataTables JS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/datatables/1.10.21/js/jquery.dataTables.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/datatables/1.10.21/js/dataTables.bootstrap5.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.rawgit.com/valdrin/flagstrap/3f8485a6/dist/js/flagstrap.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.0.13/dist/js/select2.min.js"></script>

    <script type="text/javascript">
        <?php if(session() -> has('message')): ?>
        Swal.fire({
            title: "<?php echo e(session()->get('title') ?? 'success!'); ?>",
            html: "<?php echo e(@ucwords(preg_replace('/(?<!\ )[A-Z]/', ' $0', session()->get('message')))); ?>",
            icon: "<?php echo e(session()->get('type') ?? 'success'); ?>",
            timer: 5000,
            buttons: false,
        });
        <?php endif; ?>
        <?php if(session() -> has('flash_message')): ?>
        Swal.fire({
            title: "<?php echo e(@ucwords(preg_replace('/(?<!\ )[A-Z]/', ' $0', session()->get('flash_message')))); ?>",
            icon: "<?php echo e(session()->get('type') ?? 'success'); ?>",
            timer: 5000,
            buttons: false,
        });
        <?php endif; ?>
        //delete button confirm swal dynamic.
        function showDeleteConfirmation(button) {
            Swal.fire({
                title: 'Confirm Delete',
                text: 'Are you sure you want to delete this item?',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Delete',
                cancelButtonText: 'Cancel'
            }).then((result) => {
                if (result.isConfirmed) {
                    button.closest('.delete-form').submit();
                }
            });
        }

        $(function() {
            $('[data-toggle="tooltip"]').tooltip()
        })

        function togglePassword() {
            var passwordInput = document.getElementById("userPassword");
            var passwordIcon = document.getElementById("passwordIcon");

            if (passwordInput.type === "password") {
                passwordInput.type = "text";
                passwordIcon.classList.remove("fa-eye");
                passwordIcon.classList.add("fa-eye-slash");
            } else {
                passwordInput.type = "password";
                passwordIcon.classList.remove("fa-eye-slash");
                passwordIcon.classList.add("fa-eye");
            }
        }
    </script>

    <script>
        // Simulate progress animation
        let progress = 0;
        const progressBar = document.getElementById('progressBar');
        const progressPercent = document.getElementById('progressPercent');

        function updateProgress() {
            progress += Math.random() * 15;
            if (progress > 100) progress = 100;

            progressBar.style.width = progress + '%';
            progressPercent.textContent = Math.round(progress);

            if (progress < 100) {
                setTimeout(updateProgress, 1000 + Math.random() * 2000);
            } else {
                // Simulate completion
                setTimeout(() => {
                    showCompletion();
                }, 1000);
            }
        }

        function showCompletion() {
            document.querySelector('.progress-text').textContent = 'Process Complete!';
            document.querySelector('.progress-subtitle').textContent = 'Your request has been processed successfully.';

            // Update status icons
            const statusItems = document.querySelectorAll('.status-item');
            statusItems.forEach((item, index) => {
                const icon = item.querySelector('.status-icon');
                icon.className = 'fas fa-check-circle status-icon status-complete';
            });

            // Hide cancel button and show success message
            document.querySelector('.btn-cancel').style.display = 'none';

            // Add success message
            const successDiv = document.createElement('div');
            successDiv.className = 'alert alert-success mt-3';
            successDiv.innerHTML = '<i class="fas fa-check-circle me-2"></i>Operation completed successfully!';
            document.querySelector('.progress-container').appendChild(successDiv);
        }

        function cancelProcess() {
            if (confirm('Are you sure you want to cancel this process?')) {
                document.querySelector('.progress-text').textContent = 'Process Cancelled';
                document.querySelector('.progress-subtitle').textContent = 'The operation has been cancelled.';

                // Stop progress animation
                progressBar.style.width = '0%';
                progressPercent.textContent = '0';

                // Show cancelled message
                const cancelDiv = document.createElement('div');
                cancelDiv.className = 'alert alert-warning mt-3';
                cancelDiv.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i>Operation was cancelled.';
                document.querySelector('.progress-container').appendChild(cancelDiv);

                document.querySelector('.btn-cancel').style.display = 'none';
            }
        }

        // Start progress simulation when page loads
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(updateProgress, 1000);
        });
    </Script>

    <script>
        let userTable;

        $(document).ready(function() {
            userTable = $('.user-table').DataTable({
                // Remove 'ordering: false' to allow sorting by default
                pageLength: 10,
                responsive: true,
                // pagination: true,
                language: {
                    paginate: {
                        previous: '<i class="fas fa-chevron-left"></i> Previous',
                        next: 'Next <i class="fas fa-chevron-right"></i>'
                    },
                    info: "Showing _START_ to _END_ of _TOTAL_ entries"
                },
                dom: 't<"row custom-tabel-footer"<"col-sm-6"i><"col-sm-12"p>>'
            });

            $('#customSearch').on('keyup', function() {
                userTable.search(this.value).draw();
            });

            $('.nav-link').on('click', function(e) {
                e.preventDefault();

                $('.nav-link').removeClass('active');
                $(this).addClass('active');


            });

            user - table.column(2).search('Admin').draw();
        });

        function toggleStatusStatic(checkbox) {
            const statusText = $(checkbox).closest('td').find('.status-text');

            if (checkbox.checked) {
                statusText.text('Active').removeClass('status-inactive').addClass('status-active');
            } else {
                statusText.text('Deactive').removeClass('status-active').addClass('status-inactive');
            }
        }
        $(document).ready(function() {
            $('.dropdown-icon').on('click', function() {
                var dropdownContent = $(this).closest('td').find('.user-info-dropdown-content');
                var icon = $(this).find('i');

                if (icon.hasClass('fa-chevron-down')) {
                    icon.removeClass('fa-chevron-down').addClass('fa-chevron-up'); 
                } else {
                    icon.removeClass('fa-chevron-up').addClass('fa-chevron-down');
                }

                if (dropdownContent.hasClass('d-none')) {
                    dropdownContent.removeClass('d-none').css('display', 'block'); 
                } else {
                    dropdownContent.addClass('d-none').css('display', 'none'); 
                }
            });
        });
    </script>

    <script>
    $(document).ready(function() {
        var selectedPorts = [];

        $('.select2').select2({
            placeholder: 'Add port (e.g., Shanghai)',
            allowClear: true
        });

        $('#departurePorts').on('select2:select', function(e) {
            var selectedValue = e.params.data.id;
            var selectedText = e.params.data.text;

            if (!selectedPorts.includes(selectedValue)) {
                selectedPorts.push(selectedValue);
                addPortTag(selectedValue, selectedText);
            }

            $(this).val(null).trigger('change');
            updateHiddenInput();
        });

        function addPortTag(value, text) {
            var selectedPortsDiv = $('#selectedPorts');
            var portElement = $('<div class="port-tag">' + text + ' <span class="remove-port" data-value="' + value + '">&times;</span></div>');
            selectedPortsDiv.append(portElement);

            // Disable the option in dropdown
            $('#departurePorts option[value="' + value + '"]').prop('disabled', true);
            $('#departurePorts').trigger('change');

            // Bind remove event
            portElement.find('.remove-port').on('click', function() {
                var valueToRemove = $(this).data('value');
                selectedPorts = selectedPorts.filter(function(port) {
                    return port !== valueToRemove;
                });
                $(this).parent().remove();

                // Re-enable the option in dropdown
                $('#departurePorts option[value="' + valueToRemove + '"]').prop('disabled', false);

                // Clear the select value and refresh Select2
                $('#departurePorts').val(null).trigger('change');

                updateHiddenInput();
            });
        }

        function updateHiddenInput() {
            $('#departurePorts').val(selectedPorts);
        }


    });
</script>

<script>
    $(document).ready(function () {
    var contactCounter = 1;

    $(".add-contact").on("click", function (e) {
        e.preventDefault();

        var newContactForm = $(".contact-main").first().clone();

        newContactForm.find("input, select").val("");

        // Add remove button to new contact form
        newContactForm.prepend('<i class="fas fa-trash-alt remove-btn"></i>');

        // Make unique names for radio buttons
        var uniqueChannelName = "channel_" + contactCounter;
        newContactForm.find('input[name="channel"]').attr('name', uniqueChannelName);

        // Update IDs to be unique
        newContactForm.find('#whatsapp').attr('id', 'whatsapp_' + contactCounter);
        newContactForm.find('#wechat').attr('id', 'wechat_' + contactCounter);

        // Update labels to match new IDs
        newContactForm.find('label[for="whatsapp"]').attr('for', 'whatsapp_' + contactCounter);
        newContactForm.find('label[for="wechat"]').attr('for', 'wechat_' + contactCounter);

        newContactForm.addClass("mt-10");

        $(".contacts").append(newContactForm);

        contactCounter++;
    });

    $(document).on("click", ".remove-btn", function () {
        $(this).closest(".contact-main").remove();
    });
});

</script>


    <?php echo $__env->yieldPushContent('js'); ?>
</body>

</html><?php /**PATH D:\xampp\htdocs\git\oka_paper\resources\views/theme/layout/master.blade.php ENDPATH**/ ?>