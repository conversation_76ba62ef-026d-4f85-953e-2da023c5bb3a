<div class="offcanvas offcanvas-end" tabindex="-1" id="addJumboOffcanvas" aria-labelledby="addJumboOffcanvasLabel">
    <div class="offcanvas-header">
        <h5 class="offcanvas-title" id="addJumboOffcanvasLabel">Add Jumbo & Generate Barcode</h5>
        <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
    </div>
    <div class="offcanvas-body">
        <form class="h-100 d-flex flex-column">
            <div class="flex-grow-1">
                <div class="mb-3">
                    <label for="jumboNumber" class="form-label">Jumbo #</label>
                    <input type="text" class="form-control" id="jumboNumber" value="JUMB-001" readonly>
                </div>

                <div class="mb-3">
                    <label for="purchaseOrderNo" class="form-label">Purchase Order No.</label>
                    <input type="text" class="form-control" id="purchaseOrderNo" value="PO-001">
                </div>

                <div class="mb-3">
                    <label for="widthMm" class="form-label">Width (mm)</label>
                    <input type="text" class="form-control" id="widthMm" placeholder="Enter width">
                </div>

                <div class="mb-3">
                    <label for="lengthMm" class="form-label">Length (mm)</label>
                    <input type="text" class="form-control" id="lengthMm" placeholder="Enter length">
                </div>

                <div class="mb-3">
                    <label for="rollQuantity" class="form-label">Roll Quantity</label>
                    <input type="text" class="form-control" id="rollQuantity" placeholder="Enter roll quantity">
                </div>

                <div class="mb-3">
                    <label for="entryDate" class="form-label">Entry Date</label>
                    <div class="input-group">
                        <input type="text" class="form-control" id="entryDate" placeholder="Select user role" readonly>
                        <span class="input-group-text">
                            <i class="fas fa-calendar"></i>
                        </span>
                    </div>
                </div>

                <div class="mb-4">
                    <label for="squareMeters" class="form-label">m²</label>
                    <input type="text" class="form-control" id="squareMeters">
                </div>
            </div>

            <div class="row g-2 mt-auto">
                <div class="col">
                    <button type="button" class="btn btn-outline-secondary w-100" data-bs-dismiss="offcanvas">
                        Cancel
                    </button>
                </div>
                <div class="col">
                    <button type="submit" class="btn text-white w-100" style="background-color: #D2691E;">
                        Save Jumbo & Generate Barcode
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const widthInput = document.getElementById('widthMm');
    const lengthInput = document.getElementById('lengthMm');
    const squareMetersInput = document.getElementById('squareMeters');

    function calculateSquareMeters() {
        const width = parseFloat(widthInput.value);
        const length = parseFloat(lengthInput.value);

        if (!isNaN(width) && !isNaN(length) && width > 0 && length > 0) {
            const widthInMeters = width / 1000;
            const lengthInMeters = length / 1000;
            const area = widthInMeters * lengthInMeters;
            squareMetersInput.value = area.toFixed(4);
        } else {
            squareMetersInput.value = '';
        }
    }

    if (widthInput && lengthInput && squareMetersInput) {
        widthInput.addEventListener('input', calculateSquareMeters);
        lengthInput.addEventListener('input', calculateSquareMeters);
    }

    const entryDateInput = document.getElementById('entryDate');
    if (entryDateInput && typeof flatpickr !== 'undefined') {
        flatpickr("#entryDate", {
            dateFormat: "Y-m-d",
            defaultDate: new Date(),
            onChange: function(selectedDates, dateStr, instance) {
                if (dateStr) {
                    instance.input.placeholder = dateStr;
                }
            }
        });
    }
});
</script>